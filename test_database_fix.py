#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库插入修复
"""

from api_clients import APIClientFactory, parse_task_data, parse_request_data
from database import DatabaseManager
import json

# 测试数据
TOKENS = {
    "panda": "AKIDa70b4e094e386f6ad010f5ca15e97fd0",
    "clover": "PDb96567483e354c01bf574d3fac0a457a",
    "sihai": "13ea0139-5be3-4ff8-b140-06a8015a0014"
}

UID = "2159916747338847"
SEC_UID = "MS4wLjABAAAAkgfQBpKUbpa64LtiHKDirZJywNh9Lu1zXHZUOyEXN8lhtDO0eeg0v40_ktynxZ73"

def test_database_operations():
    """测试数据库操作"""
    print("🔧 测试数据库插入修复")
    print("=" * 60)
    
    # 初始化数据库
    db = DatabaseManager()
    
    results = {}
    
    # 测试每个平台
    for platform in ["panda", "clover", "sihai"]:
        print(f"\n📊 测试{platform}平台数据库操作")
        print("-" * 40)
        
        try:
            # 创建API客户端
            if platform == "sihai":
                credentials = {"token": TOKENS[platform]}
            else:
                credentials = {"key": TOKENS[platform]}
            
            client = APIClientFactory.create_client(platform, credentials)
            if not client:
                print(f"❌ 无法创建{platform}平台客户端")
                results[platform] = False
                continue
            
            # 构建请求参数
            user_params = {
                "uid": UID,
                "sec_uid": SEC_UID,
                "platform": "dy",
                "type": "dz"
            }
            
            # 发送请求
            response = client.get_task(user_params)
            print(f"API响应: {response.get('success', response.get('code'))}")
            
            # 解析请求数据
            request_data = parse_request_data(platform, response, UID, SEC_UID)
            print(f"请求数据类型检查:")
            for key, value in request_data.items():
                print(f"  {key}: {type(value).__name__} = {value}")
            
            # 插入请求记录
            request_id = db.insert_request(request_data)
            print(f"✅ 请求记录插入成功，ID: {request_id}")
            
            # 检查是否有任务
            if request_data["has_task"]:
                # 解析任务数据
                task_data = parse_task_data(platform, response, UID)
                if task_data:
                    print(f"任务数据类型检查:")
                    for key, value in task_data.items():
                        print(f"  {key}: {type(value).__name__} = {value}")
                    
                    # 插入任务记录
                    task_id = db.insert_task(task_data)
                    print(f"✅ 任务记录插入成功，ID: {task_id}")
                else:
                    print("⚠️ 任务数据解析失败")
            else:
                print("ℹ️ 暂无任务")
            
            client.close()
            results[platform] = True
            
        except Exception as e:
            print(f"❌ {platform}平台测试失败: {e}")
            results[platform] = False
    
    # 显示总结
    print("\n" + "=" * 60)
    print("🎯 数据库操作测试总结")
    print("=" * 60)
    
    all_success = True
    for platform, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{platform}平台: {status}")
        if not success:
            all_success = False
    
    print()
    if all_success:
        print("🎉 数据库插入修复成功！所有平台都能正常插入数据。")
    else:
        print("⚠️ 部分平台仍有问题，需要进一步检查。")
    
    return results

if __name__ == "__main__":
    test_database_operations()
