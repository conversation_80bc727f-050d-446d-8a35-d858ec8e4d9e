#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三叶草平台Token测试
"""

import requests
import json

# 测试不同的Token
TOKENS_TO_TEST = [
    "AKID99fd081a6d4cb407309dd1aa788f3040",  # 你提供的
    "PDb96567483e354c01bf574d3fac0a457a"    # 文档示例中的
]

UID = "2159916747338847"
SEC_UID = "MS4wLjABAAAAkgfQBpKUbpa64LtiHKDirZJywNh9Lu1zXHZUOyEXN8lhtDO0eeg0v40_ktynxZ73"

def test_clover_token(token, token_name):
    """测试三叶草平台Token"""
    print(f"测试三叶草平台Token: {token_name}")
    print(f"Token: {token}")
    print("-" * 40)
    
    url = "http://www.sanyecao.co:98/pull"
    params = {
        "key": token,
        "uid": UID,
        "sec_uid": SEC_UID,
        "type": "dz"
    }
    
    headers = {
        'Host': 'www.sanyecao.co:98',
        'User-Agent': 'Go-http-client/1.1',
        'Accept-Encoding': 'gzip'
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("响应JSON:")
                print(json.dumps(result, ensure_ascii=False, indent=2))
                
                if result.get("code") == 0:
                    print("✅ Token有效，成功获取任务")
                    return True
                elif result.get("code") == 1001:
                    print("❌ Token无效 - 令牌异常")
                    return False
                else:
                    print(f"⚠️ 其他响应码: {result.get('code')} - {result.get('msg')}")
                    return False
            except json.JSONDecodeError:
                print("响应内容（非JSON）:")
                print(response.text)
                return False
        else:
            print("响应内容:")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"请求异常: {e}")
        return False

def main():
    print("🔍 三叶草平台Token测试")
    print("=" * 50)
    
    results = {}
    
    for i, token in enumerate(TOKENS_TO_TEST):
        token_name = f"Token{i+1}" + (" (你提供的)" if i == 0 else " (文档示例)")
        results[token_name] = test_clover_token(token, token_name)
        print()
    
    print("=" * 50)
    print("测试总结:")
    for token_name, success in results.items():
        status = "✅ 有效" if success else "❌ 无效"
        print(f"{token_name}: {status}")

if __name__ == "__main__":
    main()
