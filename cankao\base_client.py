#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础平台API客户端
"""

import requests
import time
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from models import PlatformConfig

logger = logging.getLogger(__name__)


class BasePlatformClient(ABC):
    """平台API客户端基类"""
    
    def __init__(self, config: PlatformConfig, credentials: Dict[str, str]):
        """
        初始化客户端
        
        Args:
            config: 平台配置
            credentials: 认证信息
        """
        self.config = config
        self.credentials = credentials
        
        # 创建会话
        self.session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=config.retry_count,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 设置默认请求头
        self.session.headers.update({
            'User-Agent': 'Go-http-client/1.1',
            'Accept-Encoding': 'gzip'
        })
    
    @abstractmethod
    def get_task(self, user_params: Dict[str, str]) -> Dict[str, Any]:
        """
        获取任务
        
        Args:
            user_params: 用户参数
            
        Returns:
            任务数据
        """
        pass
    
    @abstractmethod
    def submit_task(self, task_id: str, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        提交任务结果
        
        Args:
            task_id: 任务ID
            result: 执行结果
            
        Returns:
            提交结果
        """
        pass
    
    def _make_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """
        统一请求处理
        
        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 其他请求参数
            
        Returns:
            响应对象
            
        Raises:
            requests.RequestException: 请求异常
        """
        # 设置超时
        kwargs.setdefault('timeout', self.config.timeout)
        
        # 记录请求日志
        logger.info(f"🌐 {self.config.name}平台 -> 请求: {method} {url}")
        if 'params' in kwargs:
            logger.info(f"📋 请求参数: {kwargs['params']}")
        if 'data' in kwargs:
            logger.info(f"📋 请求数据: {kwargs['data']}")
        
        try:
            # 发送请求
            response = self.session.request(method, url, **kwargs)
            
            # 记录响应日志
            logger.info(f"✅ {self.config.name}平台 -> 响应: {response.status_code}")
            logger.debug(f"📄 响应内容: {response.text}")
            
            # 检查响应状态
            response.raise_for_status()
            
            return response
            
        except requests.exceptions.Timeout:
            logger.error(f"⏰ {self.config.name}平台请求超时")
            raise
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ {self.config.name}平台请求失败: {e}")
            raise
    
    def _create_no_task_response(self) -> Dict[str, Any]:
        """创建无任务响应"""
        return {
            "success": False,
            "code": 406,
            "data": {},
            "msg": "无任务"
        }
    
    def _create_error_response(self, error_msg: str) -> Dict[str, Any]:
        """创建错误响应"""
        return {
            "success": False,
            "code": 500,
            "data": {},
            "msg": error_msg
        }
    
    def _create_success_response(self, data: Any = None, msg: str = "success") -> Dict[str, Any]:
        """创建成功响应"""
        return {
            "success": True,
            "code": 0,
            "data": data or {},
            "msg": msg
        }
    
    def close(self):
        """关闭会话"""
        if self.session:
            self.session.close()
