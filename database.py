#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库操作模块
"""

import sqlite3
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "task_monitor.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库表"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 创建任务记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id TEXT NOT NULL,
                    platform TEXT NOT NULL,
                    sent_uid TEXT NOT NULL,
                    received_vid TEXT,
                    sec_uid TEXT,
                    share_url TEXT,
                    price REAL,
                    task_type TEXT,
                    nickname TEXT,
                    created_at TIMESTAMP DEFAULT (datetime('now', 'localtime')),
                    raw_data TEXT
                )
            ''')
            
            # 创建请求记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS requests (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    platform TEXT NOT NULL,
                    request_type TEXT NOT NULL,
                    sent_uid TEXT NOT NULL,
                    sec_uid TEXT,
                    success BOOLEAN NOT NULL,
                    response_code INTEGER,
                    response_message TEXT,
                    has_task BOOLEAN DEFAULT FALSE,
                    task_id TEXT,
                    created_at TIMESTAMP DEFAULT (datetime('now', 'localtime')),
                    raw_response TEXT
                )
            ''')
            
            # 创建统计视图
            cursor.execute('''
                CREATE VIEW IF NOT EXISTS daily_stats AS
                SELECT 
                    platform,
                    DATE(created_at) as date,
                    COUNT(*) as total_requests,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_requests,
                    SUM(CASE WHEN has_task = 1 THEN 1 ELSE 0 END) as tasks_received,
                    ROUND(AVG(CASE WHEN has_task = 1 THEN 1.0 ELSE 0.0 END) * 100, 2) as task_rate
                FROM requests
                GROUP BY platform, DATE(created_at)
                ORDER BY date DESC, platform
            ''')
            
            conn.commit()
            logger.info("✅ 数据库初始化完成")
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        try:
            yield conn
        finally:
            conn.close()
    
    def insert_task(self, task_data: Dict[str, Any]) -> int:
        """
        插入任务记录
        
        Args:
            task_data: 任务数据
            
        Returns:
            插入的记录ID
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO tasks (
                    task_id, platform, sent_uid, received_vid, sec_uid,
                    share_url, price, task_type, nickname, raw_data
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                task_data.get('task_id', ''),
                task_data.get('platform', ''),
                task_data.get('sent_uid', ''),
                task_data.get('received_vid', ''),
                task_data.get('sec_uid', ''),
                task_data.get('share_url', ''),
                task_data.get('price', 0.0),
                task_data.get('task_type', ''),
                task_data.get('nickname', ''),
                task_data.get('raw_data', '')
            ))
            conn.commit()
            return cursor.lastrowid
    
    def insert_request(self, request_data: Dict[str, Any]) -> int:
        """
        插入请求记录
        
        Args:
            request_data: 请求数据
            
        Returns:
            插入的记录ID
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO requests (
                    platform, request_type, sent_uid, sec_uid, success,
                    response_code, response_message, has_task, task_id, raw_response
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                request_data.get('platform', ''),
                request_data.get('request_type', 'get_task'),
                request_data.get('sent_uid', ''),
                request_data.get('sec_uid', ''),
                request_data.get('success', False),
                request_data.get('response_code', 0),
                request_data.get('response_message', ''),
                request_data.get('has_task', False),
                request_data.get('task_id', ''),
                request_data.get('raw_response', '')
            ))
            conn.commit()
            return cursor.lastrowid
    
    def get_daily_stats(self, days: int = 7) -> List[Dict[str, Any]]:
        """
        获取每日统计数据
        
        Args:
            days: 查询天数
            
        Returns:
            统计数据列表
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM daily_stats
                WHERE date >= date('now', '-{} days')
                ORDER BY date DESC, platform
            '''.format(days))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def get_platform_summary(self) -> List[Dict[str, Any]]:
        """
        获取平台汇总统计
        
        Returns:
            平台统计数据
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT 
                    platform,
                    COUNT(*) as total_requests,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_requests,
                    SUM(CASE WHEN has_task = 1 THEN 1 ELSE 0 END) as tasks_received,
                    ROUND(AVG(CASE WHEN has_task = 1 THEN 1.0 ELSE 0.0 END) * 100, 2) as task_rate,
                    MIN(created_at) as first_request,
                    MAX(created_at) as last_request
                FROM requests
                GROUP BY platform
                ORDER BY platform
            ''')
            
            return [dict(row) for row in cursor.fetchall()]
    
    def get_hourly_task_distribution(self, platform: str = None) -> List[Dict[str, Any]]:
        """
        获取小时级任务分布
        
        Args:
            platform: 平台名称，None表示所有平台
            
        Returns:
            小时级分布数据
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            where_clause = "WHERE platform = ?" if platform else ""
            params = [platform] if platform else []
            
            cursor.execute(f'''
                SELECT 
                    strftime('%H', created_at) as hour,
                    COUNT(*) as total_requests,
                    SUM(CASE WHEN has_task = 1 THEN 1 ELSE 0 END) as tasks_received
                FROM requests
                {where_clause}
                GROUP BY strftime('%H', created_at)
                ORDER BY hour
            ''', params)
            
            return [dict(row) for row in cursor.fetchall()]
    
    def cleanup_old_data(self, days: int = 30):
        """
        清理旧数据
        
        Args:
            days: 保留天数
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 清理旧的请求记录
            cursor.execute('''
                DELETE FROM requests 
                WHERE created_at < date('now', '-{} days')
            '''.format(days))
            
            # 清理旧的任务记录
            cursor.execute('''
                DELETE FROM tasks 
                WHERE created_at < date('now', '-{} days')
            '''.format(days))
            
            conn.commit()
            logger.info(f"✅ 清理了{days}天前的旧数据")
