#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础tkinter测试
"""

import tkinter as tk
from tkinter import messagebox

def test_click():
    messagebox.showinfo("测试", "基础tkinter工作正常！")

def main():
    root = tk.Tk()
    root.title("基础tkinter测试")
    root.geometry("300x200")
    
    label = tk.Label(root, text="基础tkinter测试", font=("Arial", 16))
    label.pack(pady=20)
    
    button = tk.Button(root, text="点击测试", command=test_click)
    button.pack(pady=10)
    
    print("基础tkinter测试窗口已启动")
    root.mainloop()

if __name__ == "__main__":
    main()
