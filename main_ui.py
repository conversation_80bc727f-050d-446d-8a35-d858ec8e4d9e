#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主UI界面
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import logging
from datetime import datetime
from typing import Dict, Any, List

from database import DatabaseManager
from api_clients import APIClientFactory, parse_task_data, parse_request_data
from statistics_window import StatisticsWindow
from config_manager import config_manager

# 设置customtkinter主题
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

logger = logging.getLogger(__name__)


class TestInstance:
    """测试实例类"""
    
    def __init__(self, parent, instance_id: int):
        self.parent = parent
        self.instance_id = instance_id
        self.is_running = False
        self.thread = None

        # 创建UI组件
        self.create_widgets()

        # 加载配置
        self.load_config()
    
    def create_widgets(self):
        """创建UI组件"""
        # 主框架
        self.frame = ctk.CTkFrame(self.parent)
        # 不在这里pack，由父组件控制布局

        # 标题
        title_label = ctk.CTkLabel(
            self.frame,
            text=f"实例 {self.instance_id}",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        title_label.pack(pady=(5, 3))
        
        # 输入区域
        input_frame = ctk.CTkFrame(self.frame)
        input_frame.pack(fill="x", padx=5, pady=3)

        # Token输入
        ctk.CTkLabel(input_frame, text="Token/Key:", font=ctk.CTkFont(size=10)).pack(anchor="w", padx=3)
        self.token_entry = ctk.CTkEntry(input_frame, height=25, placeholder_text="输入Token或Key")
        self.token_entry.pack(fill="x", padx=3, pady=1)
        self.token_entry.bind("<FocusOut>", lambda e: self.save_field_config("token", self.token_entry.get()))

        # 平台选择
        ctk.CTkLabel(input_frame, text="平台:", font=ctk.CTkFont(size=10)).pack(anchor="w", padx=3)
        self.platform_combo = ctk.CTkComboBox(
            input_frame,
            values=["panda", "clover", "sihai"],
            height=25,
            command=lambda value: self.save_field_config("platform", value)
        )
        self.platform_combo.pack(fill="x", padx=3, pady=1)

        # UID输入
        ctk.CTkLabel(input_frame, text="UID:", font=ctk.CTkFont(size=10)).pack(anchor="w", padx=3)
        self.uid_entry = ctk.CTkEntry(input_frame, height=25, placeholder_text="输入UID")
        self.uid_entry.pack(fill="x", padx=3, pady=1)
        self.uid_entry.bind("<FocusOut>", lambda e: self.save_field_config("uid", self.uid_entry.get()))

        # sec_uid输入
        ctk.CTkLabel(input_frame, text="sec_uid:", font=ctk.CTkFont(size=10)).pack(anchor="w", padx=3)
        self.sec_uid_entry = ctk.CTkEntry(input_frame, height=25, placeholder_text="输入sec_uid")
        self.sec_uid_entry.pack(fill="x", padx=3, pady=1)
        self.sec_uid_entry.bind("<FocusOut>", lambda e: self.save_field_config("sec_uid", self.sec_uid_entry.get()))

        # 间隔时间输入
        ctk.CTkLabel(input_frame, text="间隔(秒):", font=ctk.CTkFont(size=10)).pack(anchor="w", padx=3)
        self.interval_entry = ctk.CTkEntry(input_frame, height=25, placeholder_text="10")
        self.interval_entry.pack(fill="x", padx=3, pady=1)
        self.interval_entry.bind("<FocusOut>", lambda e: self.save_field_config("interval", self.interval_entry.get()))

        # 控制按钮区域
        button_frame = ctk.CTkFrame(input_frame)
        button_frame.pack(fill="x", padx=3, pady=3)

        self.start_button = ctk.CTkButton(
            button_frame,
            text="开始",
            command=self.start_test,
            height=25,
            font=ctk.CTkFont(size=10)
        )
        self.start_button.pack(side="left", fill="x", expand=True, padx=1)

        self.stop_button = ctk.CTkButton(
            button_frame,
            text="停止",
            command=self.stop_test,
            height=25,
            font=ctk.CTkFont(size=10),
            state="disabled"
        )
        self.stop_button.pack(side="right", fill="x", expand=True, padx=1)

        # 状态标签
        self.status_label = ctk.CTkLabel(input_frame, text="状态: 未开始", font=ctk.CTkFont(size=10))
        self.status_label.pack(pady=2)
        
        # 日志区域
        log_frame = ctk.CTkFrame(self.frame)
        log_frame.pack(fill="both", expand=True, padx=5, pady=3)

        ctk.CTkLabel(log_frame, text="日志输出:", font=ctk.CTkFont(size=10, weight="bold")).pack(anchor="w", padx=3, pady=(3, 0))

        # 创建日志文本框和滚动条的容器
        log_container = tk.Frame(log_frame, bg="#2b2b2b")
        log_container.pack(fill="both", expand=True, padx=3, pady=3)

        # 使用tkinter的Text组件作为日志输出
        self.log_text = tk.Text(
            log_container,
            height=12,
            bg="#2b2b2b",
            fg="#ffffff",
            font=("Consolas", 8),
            wrap=tk.WORD,
            state=tk.DISABLED
        )

        # 添加滚动条
        scrollbar = ttk.Scrollbar(log_container, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        # 布局日志文本框和滚动条
        self.log_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def load_config(self):
        """加载配置"""
        try:
            config = config_manager.get_instance_config(self.instance_id)

            # 设置各个字段的值
            if config.get("token"):
                self.token_entry.insert(0, config["token"])

            if config.get("platform"):
                self.platform_combo.set(config["platform"])

            if config.get("uid"):
                self.uid_entry.insert(0, config["uid"])

            if config.get("sec_uid"):
                self.sec_uid_entry.insert(0, config["sec_uid"])

            if config.get("interval"):
                self.interval_entry.insert(0, config["interval"])

            self.log_message(f"✅ 实例{self.instance_id}配置已加载")

        except Exception as e:
            self.log_message(f"⚠️ 加载配置失败: {str(e)}")

    def save_field_config(self, field: str, value: str):
        """保存字段配置"""
        try:
            config_manager.update_instance_config(self.instance_id, field, value)
            logger.debug(f"📝 实例{self.instance_id}字段{field}已保存: {value}")
        except Exception as e:
            logger.error(f"❌ 保存配置失败: {e}")
    
    def log_message(self, message: str):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        # 在主线程中更新UI
        self.parent.after(0, self._update_log, log_entry)
    
    def _update_log(self, log_entry: str):
        """更新日志显示"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # 限制日志行数为100条
        lines = self.log_text.get("1.0", tk.END).split("\n")
        if len(lines) > 100:
            # 删除最旧的日志，保留最新的100条
            lines_to_delete = len(lines) - 100
            self.log_text.delete("1.0", f"{lines_to_delete}.0")

        self.log_text.config(state=tk.DISABLED)
    
    def start_test(self):
        """开始测试"""
        if self.is_running:
            return
        
        # 验证输入
        if not self.token_entry.get().strip():
            messagebox.showerror("错误", "请输入Token/Key")
            return
        
        if not self.uid_entry.get().strip():
            messagebox.showerror("错误", "请输入UID")
            return
        
        # 更新UI状态
        self.is_running = True
        self.start_button.configure(state="disabled")
        self.stop_button.configure(state="normal")
        self.status_label.configure(text="状态: 运行中")
        
        # 启动测试线程
        self.thread = threading.Thread(target=self._test_loop, daemon=True)
        self.thread.start()
        
        self.log_message("✅ 开始测试")
    
    def stop_test(self):
        """停止测试"""
        if not self.is_running:
            return
        
        self.is_running = False
        self.start_button.configure(state="normal")
        self.stop_button.configure(state="disabled")
        self.status_label.configure(text="状态: 已停止")
        
        self.log_message("⏹️ 停止测试")
    
    def _test_loop(self):
        """测试循环"""
        platform = self.platform_combo.get()
        token = self.token_entry.get().strip()
        uid = self.uid_entry.get().strip()
        sec_uid = self.sec_uid_entry.get().strip()
        
        try:
            interval = float(self.interval_entry.get() or "10")
        except ValueError:
            interval = 10.0
        
        # 创建API客户端
        if platform == "sihai":
            credentials = {"token": token}
        else:
            credentials = {"key": token}
        
        client = APIClientFactory.create_client(platform, credentials)
        if not client:
            self.log_message(f"❌ 无法创建{platform}平台客户端")
            self.stop_test()
            return
        
        db = DatabaseManager()
        request_count = 0
        task_count = 0
        
        self.log_message(f"🚀 开始测试{platform}平台，间隔{interval}秒")
        
        try:
            while self.is_running:
                try:
                    # 构建请求参数
                    user_params = {
                        "uid": uid,
                        "sec_uid": sec_uid,
                        "platform": "dy",
                        "type": "dz"
                    }
                    
                    # 发送请求
                    response = client.get_task(user_params)
                    request_count += 1
                    
                    # 解析请求数据
                    request_data = parse_request_data(platform, response, uid, sec_uid)
                    db.insert_request(request_data)
                    
                    # 检查是否有任务
                    if request_data["has_task"]:
                        task_count += 1
                        # 解析任务数据
                        task_data = parse_task_data(platform, response, uid)
                        if task_data:
                            db.insert_task(task_data)
                            self.log_message(f"✅ 获取到任务: {task_data['task_id']}")
                        else:
                            self.log_message("⚠️ 任务数据解析失败")
                    else:
                        self.log_message(f"ℹ️ 无任务 - {request_data['response_message']}")
                    
                    # 更新统计
                    self.log_message(f"📊 请求: {request_count}, 任务: {task_count}")
                    
                except Exception as e:
                    self.log_message(f"❌ 请求异常: {str(e)}")
                
                # 等待间隔
                time.sleep(interval)
                
        except Exception as e:
            self.log_message(f"❌ 测试循环异常: {str(e)}")
        finally:
            client.close()
            if self.is_running:
                self.stop_test()


class MainApplication:
    """主应用程序"""
    
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("任务平台测试工具")

        # 加载窗口配置
        self.load_window_config()

        # 初始化数据库
        self.db = DatabaseManager()

        # 创建UI
        self.create_widgets()

        # 设置日志
        self.setup_logging()

        # 绑定窗口事件
        self.bind_window_events()
    
    def create_widgets(self):
        """创建主界面组件"""
        # 标题
        title_label = ctk.CTkLabel(
            self.root,
            text="任务平台API测试工具",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=10)

        # 创建主容器框架
        main_container = ctk.CTkFrame(self.root)
        main_container.pack(fill="both", expand=True, padx=10, pady=5)

        # 创建4个测试实例的横向布局
        self.test_instances = []
        for i in range(1, 5):
            instance = TestInstance(main_container, i)
            instance.frame.pack(side="left", fill="both", expand=True, padx=5, pady=5)
            self.test_instances.append(instance)
        
        # 底部按钮区域
        button_frame = ctk.CTkFrame(self.root)
        button_frame.pack(fill="x", padx=20, pady=10)
        
        # 统计按钮
        stats_button = ctk.CTkButton(
            button_frame,
            text="查看统计",
            command=self.show_statistics,
            width=120,
            height=40
        )
        stats_button.pack(side="left", padx=10)
        
        # 清理数据按钮
        cleanup_button = ctk.CTkButton(
            button_frame,
            text="清理旧数据",
            command=self.cleanup_data,
            width=120,
            height=40
        )
        cleanup_button.pack(side="left", padx=10)

        # 配置管理按钮
        config_button = ctk.CTkButton(
            button_frame,
            text="配置管理",
            command=self.show_config_menu,
            width=120,
            height=40
        )
        config_button.pack(side="left", padx=10)

        # 退出按钮
        exit_button = ctk.CTkButton(
            button_frame,
            text="退出",
            command=self.on_closing,
            width=120,
            height=40
        )
        exit_button.pack(side="right", padx=10)
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('task_monitor.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )

    def load_window_config(self):
        """加载窗口配置"""
        try:
            window_config = config_manager.get_window_config()
            geometry = window_config.get("geometry", "1600x900")
            self.root.geometry(geometry)

            # 设置主题
            theme = window_config.get("theme", "dark")
            ctk.set_appearance_mode(theme)

        except Exception as e:
            logger.error(f"❌ 加载窗口配置失败: {e}")
            self.root.geometry("1600x900")

    def bind_window_events(self):
        """绑定窗口事件"""
        # 绑定窗口大小改变事件
        self.root.bind("<Configure>", self.on_window_configure)

    def on_window_configure(self, event):
        """窗口配置改变事件"""
        if event.widget == self.root:
            # 保存窗口大小
            geometry = self.root.geometry()
            config_manager.update_window_config("geometry", geometry)

    def show_config_menu(self):
        """显示配置管理菜单"""
        from tkinter import filedialog, messagebox

        # 创建配置菜单窗口
        config_window = ctk.CTkToplevel(self.root)
        config_window.title("配置管理")
        config_window.geometry("400x300")
        config_window.transient(self.root)
        config_window.grab_set()

        # 标题
        title_label = ctk.CTkLabel(
            config_window,
            text="配置管理",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=20)

        # 按钮框架
        button_frame = ctk.CTkFrame(config_window)
        button_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 导出配置按钮
        export_button = ctk.CTkButton(
            button_frame,
            text="导出配置",
            command=lambda: self.export_config(config_window),
            width=200,
            height=40
        )
        export_button.pack(pady=10)

        # 导入配置按钮
        import_button = ctk.CTkButton(
            button_frame,
            text="导入配置",
            command=lambda: self.import_config(config_window),
            width=200,
            height=40
        )
        import_button.pack(pady=10)

        # 重置配置按钮
        reset_button = ctk.CTkButton(
            button_frame,
            text="重置配置",
            command=lambda: self.reset_config(config_window),
            width=200,
            height=40
        )
        reset_button.pack(pady=10)

        # 关闭按钮
        close_button = ctk.CTkButton(
            button_frame,
            text="关闭",
            command=config_window.destroy,
            width=200,
            height=40
        )
        close_button.pack(pady=20)

    def export_config(self, parent_window):
        """导出配置"""
        from tkinter import filedialog, messagebox

        try:
            file_path = filedialog.asksaveasfilename(
                parent=parent_window,
                title="导出配置",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )

            if file_path:
                if config_manager.export_config(file_path):
                    messagebox.showinfo("成功", f"配置已导出到:\n{file_path}")
                else:
                    messagebox.showerror("错误", "导出配置失败")

        except Exception as e:
            messagebox.showerror("错误", f"导出配置失败: {str(e)}")

    def import_config(self, parent_window):
        """导入配置"""
        from tkinter import filedialog, messagebox

        try:
            file_path = filedialog.askopenfilename(
                parent=parent_window,
                title="导入配置",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )

            if file_path:
                result = messagebox.askyesno(
                    "确认导入",
                    "导入配置将覆盖当前所有设置，是否继续？"
                )

                if result:
                    if config_manager.import_config(file_path):
                        messagebox.showinfo("成功", "配置导入成功，请重启程序以应用新配置")
                        parent_window.destroy()
                    else:
                        messagebox.showerror("错误", "导入配置失败")

        except Exception as e:
            messagebox.showerror("错误", f"导入配置失败: {str(e)}")

    def reset_config(self, parent_window):
        """重置配置"""
        from tkinter import messagebox

        try:
            result = messagebox.askyesno(
                "确认重置",
                "重置配置将清除所有设置，是否继续？"
            )

            if result:
                config_manager.reset_config()
                messagebox.showinfo("成功", "配置已重置，请重启程序以应用默认配置")
                parent_window.destroy()

        except Exception as e:
            messagebox.showerror("错误", f"重置配置失败: {str(e)}")
    
    def show_statistics(self):
        """显示统计窗口"""
        try:
            stats_window = StatisticsWindow(self.root, self.db)
        except Exception as e:
            messagebox.showerror("错误", f"打开统计窗口失败: {str(e)}")
    
    def cleanup_data(self):
        """清理旧数据"""
        result = messagebox.askyesno(
            "确认清理",
            "确定要清理30天前的旧数据吗？\n此操作不可撤销。"
        )
        if result:
            try:
                self.db.cleanup_old_data(30)
                messagebox.showinfo("成功", "旧数据清理完成")
            except Exception as e:
                messagebox.showerror("错误", f"清理数据失败: {str(e)}")
    
    def on_closing(self):
        """关闭应用程序"""
        # 停止所有测试实例
        for instance in self.test_instances:
            if instance.is_running:
                instance.stop_test()
        
        # 等待线程结束
        time.sleep(1)
        
        self.root.destroy()
    
    def run(self):
        """运行应用程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()


if __name__ == "__main__":
    app = MainApplication()
    app.run()
