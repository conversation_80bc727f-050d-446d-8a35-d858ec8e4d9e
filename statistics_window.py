#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计窗口
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, List
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.dates as mdates
from datetime import datetime, timedelta, timezone

from database import DatabaseManager

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


def format_local_time(time_str: str) -> str:
    """
    格式化时间字符串为本地时间显示

    Args:
        time_str: 数据库中的时间字符串

    Returns:
        格式化后的本地时间字符串
    """
    if not time_str:
        return ""

    try:
        # 解析时间字符串
        dt = datetime.fromisoformat(time_str.replace('Z', '+00:00'))

        # 如果是UTC时间，转换为本地时间
        if dt.tzinfo is None:
            # 假设数据库中存储的是本地时间
            return dt.strftime('%Y-%m-%d %H:%M')
        else:
            # 转换为本地时间
            local_dt = dt.astimezone()
            return local_dt.strftime('%Y-%m-%d %H:%M')
    except:
        # 如果解析失败，直接截取前16位
        return time_str[:16] if len(time_str) >= 16 else time_str


class StatisticsWindow:
    """统计窗口"""

    def __init__(self, parent, db: DatabaseManager):
        self.parent = parent
        self.db = db
        
        # 创建窗口
        self.window = ctk.CTkToplevel(parent)
        self.window.title("统计分析")
        self.window.geometry("1000x700")
        self.window.transient(parent)
        self.window.grab_set()
        
        # 创建UI
        self.create_widgets()
        
        # 加载数据
        self.refresh_data()
    
    def create_widgets(self):
        """创建UI组件"""
        # 标题
        title_label = ctk.CTkLabel(
            self.window,
            text="任务平台统计分析",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=10)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(self.window)
        self.notebook.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 概览选项卡
        self.overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.overview_frame, text="概览统计")
        self.create_overview_tab()
        
        # 每日统计选项卡
        self.daily_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.daily_frame, text="每日统计")
        self.create_daily_tab()
        
        # 小时分布选项卡
        self.hourly_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.hourly_frame, text="小时分布")
        self.create_hourly_tab()
        
        # 底部按钮
        button_frame = ctk.CTkFrame(self.window)
        button_frame.pack(fill="x", padx=20, pady=10)
        
        refresh_button = ctk.CTkButton(
            button_frame,
            text="刷新数据",
            command=self.refresh_data,
            width=100
        )
        refresh_button.pack(side="left", padx=10)
        
        close_button = ctk.CTkButton(
            button_frame,
            text="关闭",
            command=self.window.destroy,
            width=100
        )
        close_button.pack(side="right", padx=10)
    
    def create_overview_tab(self):
        """创建概览选项卡"""
        # 创建表格框架
        table_frame = ctk.CTkFrame(self.overview_frame)
        table_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 表格标题
        ctk.CTkLabel(
            table_frame,
            text="平台汇总统计",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=10)
        
        # 创建Treeview表格
        columns = ("平台", "总请求数", "成功请求", "获得任务", "任务率(%)", "首次请求", "最后请求")
        self.overview_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=10)
        
        # 设置列标题和宽度
        for col in columns:
            self.overview_tree.heading(col, text=col)
            self.overview_tree.column(col, width=120, anchor="center")
        
        # 添加滚动条
        scrollbar_overview = ttk.Scrollbar(table_frame, orient="vertical", command=self.overview_tree.yview)
        self.overview_tree.configure(yscrollcommand=scrollbar_overview.set)
        
        # 打包组件
        self.overview_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        scrollbar_overview.pack(side="right", fill="y", pady=10)
    
    def create_daily_tab(self):
        """创建每日统计选项卡"""
        # 控制框架
        control_frame = ctk.CTkFrame(self.daily_frame)
        control_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(control_frame, text="查看天数:").pack(side="left", padx=10)
        self.days_var = tk.StringVar(value="7")
        days_combo = ctk.CTkComboBox(
            control_frame,
            values=["3", "7", "14", "30"],
            variable=self.days_var,
            command=self.refresh_daily_data,
            width=80
        )
        days_combo.pack(side="left", padx=5)
        
        # 图表框架
        self.daily_chart_frame = ctk.CTkFrame(self.daily_frame)
        self.daily_chart_frame.pack(fill="both", expand=True, padx=10, pady=5)
    
    def create_hourly_tab(self):
        """创建小时分布选项卡"""
        # 控制框架
        control_frame = ctk.CTkFrame(self.hourly_frame)
        control_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(control_frame, text="选择平台:").pack(side="left", padx=10)
        self.platform_var = tk.StringVar(value="全部")
        platform_combo = ctk.CTkComboBox(
            control_frame,
            values=["全部", "panda", "clover", "sihai"],
            variable=self.platform_var,
            command=self.refresh_hourly_data,
            width=100
        )
        platform_combo.pack(side="left", padx=5)
        
        # 图表框架
        self.hourly_chart_frame = ctk.CTkFrame(self.hourly_frame)
        self.hourly_chart_frame.pack(fill="both", expand=True, padx=10, pady=5)
    
    def refresh_data(self):
        """刷新所有数据"""
        self.refresh_overview_data()
        self.refresh_daily_data()
        self.refresh_hourly_data()
    
    def refresh_overview_data(self):
        """刷新概览数据"""
        try:
            # 清空表格
            for item in self.overview_tree.get_children():
                self.overview_tree.delete(item)
            
            # 获取数据
            data = self.db.get_platform_summary()
            
            # 填充数据
            for row in data:
                values = (
                    row["platform"],
                    row["total_requests"],
                    row["successful_requests"],
                    row["tasks_received"],
                    f"{row['task_rate']:.2f}%",
                    format_local_time(row["first_request"]),
                    format_local_time(row["last_request"])
                )
                self.overview_tree.insert("", "end", values=values)
                
        except Exception as e:
            print(f"刷新概览数据失败: {e}")
    
    def refresh_daily_data(self, *args):
        """刷新每日数据"""
        try:
            days = int(self.days_var.get())
            data = self.db.get_daily_stats(days)
            
            # 清空之前的图表
            for widget in self.daily_chart_frame.winfo_children():
                widget.destroy()
            
            if not data:
                ctk.CTkLabel(
                    self.daily_chart_frame,
                    text="暂无数据",
                    font=ctk.CTkFont(size=16)
                ).pack(expand=True)
                return
            
            # 创建图表
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))
            fig.patch.set_facecolor('#2b2b2b')
            
            # 按平台分组数据
            platforms = {}
            for row in data:
                platform = row["platform"]
                if platform not in platforms:
                    platforms[platform] = {"dates": [], "requests": [], "tasks": [], "rates": []}
                
                platforms[platform]["dates"].append(datetime.strptime(row["date"], "%Y-%m-%d"))
                platforms[platform]["requests"].append(row["total_requests"])
                platforms[platform]["tasks"].append(row["tasks_received"])
                platforms[platform]["rates"].append(row["task_rate"])
            
            # 绘制请求数图表
            ax1.set_facecolor('#2b2b2b')
            ax1.tick_params(colors='white')
            ax1.set_title("每日请求数统计", color='white', fontsize=14)
            ax1.set_ylabel("请求数", color='white')
            
            colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
            for i, (platform, pdata) in enumerate(platforms.items()):
                ax1.plot(pdata["dates"], pdata["requests"], 
                        marker='o', label=f"{platform}平台", color=colors[i % len(colors)])
            
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 绘制任务率图表
            ax2.set_facecolor('#2b2b2b')
            ax2.tick_params(colors='white')
            ax2.set_title("每日任务获取率", color='white', fontsize=14)
            ax2.set_ylabel("任务率 (%)", color='white')
            ax2.set_xlabel("日期", color='white')
            
            for i, (platform, pdata) in enumerate(platforms.items()):
                ax2.plot(pdata["dates"], pdata["rates"], 
                        marker='s', label=f"{platform}平台", color=colors[i % len(colors)])
            
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # 格式化日期轴
            for ax in [ax1, ax2]:
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
                ax.xaxis.set_major_locator(mdates.DayLocator(interval=1))
                plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
            
            plt.tight_layout()
            
            # 嵌入到tkinter
            canvas = FigureCanvasTkAgg(fig, self.daily_chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill="both", expand=True)
            
        except Exception as e:
            print(f"刷新每日数据失败: {e}")
    
    def refresh_hourly_data(self, *args):
        """刷新小时分布数据"""
        try:
            platform = self.platform_var.get()
            platform_filter = None if platform == "全部" else platform
            
            data = self.db.get_hourly_task_distribution(platform_filter)
            
            # 清空之前的图表
            for widget in self.hourly_chart_frame.winfo_children():
                widget.destroy()
            
            if not data:
                ctk.CTkLabel(
                    self.hourly_chart_frame,
                    text="暂无数据",
                    font=ctk.CTkFont(size=16)
                ).pack(expand=True)
                return
            
            # 创建图表
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))
            fig.patch.set_facecolor('#2b2b2b')
            
            hours = [int(row["hour"]) for row in data]
            requests = [row["total_requests"] for row in data]
            tasks = [row["tasks_received"] for row in data]
            
            # 绘制请求数分布
            ax1.set_facecolor('#2b2b2b')
            ax1.tick_params(colors='white')
            ax1.set_title(f"小时请求分布 - {platform}", color='white', fontsize=14)
            ax1.set_ylabel("请求数", color='white')
            ax1.bar(hours, requests, alpha=0.7, color='#1f77b4')
            ax1.grid(True, alpha=0.3)
            
            # 绘制任务数分布
            ax2.set_facecolor('#2b2b2b')
            ax2.tick_params(colors='white')
            ax2.set_title(f"小时任务分布 - {platform}", color='white', fontsize=14)
            ax2.set_ylabel("任务数", color='white')
            ax2.set_xlabel("小时", color='white')
            ax2.bar(hours, tasks, alpha=0.7, color='#ff7f0e')
            ax2.grid(True, alpha=0.3)
            
            # 设置x轴
            for ax in [ax1, ax2]:
                ax.set_xlim(-0.5, 23.5)
                ax.set_xticks(range(0, 24, 2))
            
            plt.tight_layout()
            
            # 嵌入到tkinter
            canvas = FigureCanvasTkAgg(fig, self.hourly_chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill="both", expand=True)
            
        except Exception as e:
            print(f"刷新小时数据失败: {e}")
