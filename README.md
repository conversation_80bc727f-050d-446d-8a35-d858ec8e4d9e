# 任务平台API测试工具

这是一个用于测试3个任务平台API的工具，支持熊猫、三叶草、四海平台，可以记录任务数据并进行统计分析。

## 功能特点

- 🎯 **多平台支持**: 支持熊猫、三叶草、四海三个平台
- 🔄 **并发测试**: 支持4个测试实例同时运行
- 📊 **数据记录**: 完整记录任务ID、平台、UID、VID等信息
- 📈 **统计分析**: 提供详细的统计图表和数据分析
- 💾 **SQLite存储**: 使用SQLite数据库，支持30万+记录
- 🎨 **现代UI**: 使用CustomTkinter创建现代化界面
- 💾 **自动保存配置**: 输入框失去焦点时自动保存配置
- 📱 **横向布局**: 4个测试实例横向并列显示
- 📝 **日志限制**: 每个实例最多显示100条日志

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行程序

```bash
python run.py
```

## 使用说明

### 1. 配置测试实例

每个测试实例需要配置以下参数：

- **Token/Key**: 
  - 熊猫平台和三叶草平台使用Key
  - 四海平台使用Token
- **平台**: 选择要测试的平台（panda/clover/sihai）
- **UID**: 用户ID
- **sec_uid**: 安全用户ID（可选）
- **间隔时间**: 请求间隔，单位秒（默认10秒）

### 2. 开始测试

1. 填写完配置信息后，点击"开始测试"按钮
2. 程序会按设定的间隔时间循环请求API
3. 日志区域会实时显示请求结果
4. 所有数据会自动保存到SQLite数据库

### 3. 查看统计

点击底部的"查看统计"按钮，可以查看：

- **概览统计**: 各平台的总体数据汇总
- **每日统计**: 每日请求数和任务获取率趋势图
- **小时分布**: 24小时内的请求和任务分布图

### 4. 配置管理

- **自动保存**: 输入框失去焦点时自动保存配置到 `config.json`
- **配置导入/导出**: 点击"配置管理"按钮可以导入/导出配置文件
- **配置重置**: 可以重置所有配置为默认值
- **示例配置**: 参考 `config_example.json` 文件

### 5. 数据管理

- **清理旧数据**: 可以清理30天前的历史数据
- **自动记录**: 所有请求和任务数据都会自动保存
- **日志限制**: 每个实例最多显示100条日志，自动清理旧日志

## 数据库结构

### 任务记录表 (tasks)
- `task_id`: 任务ID
- `platform`: 平台名称
- `sent_uid`: 发送的UID
- `received_vid`: 获取到的视频ID
- `sec_uid`: 安全用户ID
- `share_url`: 分享链接
- `price`: 任务价格
- `task_type`: 任务类型
- `nickname`: 用户昵称
- `created_at`: 创建时间
- `raw_data`: 原始数据

### 请求记录表 (requests)
- `platform`: 平台名称
- `request_type`: 请求类型
- `sent_uid`: 发送的UID
- `sec_uid`: 安全用户ID
- `success`: 请求是否成功
- `response_code`: 响应代码
- `response_message`: 响应消息
- `has_task`: 是否有任务
- `task_id`: 任务ID
- `created_at`: 创建时间
- `raw_response`: 原始响应

## 平台API说明

### 熊猫平台
- 请求方式: GET
- 认证方式: key参数
- 任务ID字段: studiotask_id
- 价格单位: 分（需要除以100转换为元）

### 三叶草平台
- 请求方式: GET
- 认证方式: key参数
- 任务ID字段: task_id
- 特点: 无价格信息

### 四海平台
- 请求方式: POST
- 认证方式: Token请求头
- 任务ID字段: taskLogId
- 价格单位: 元（需要乘以100）

## 注意事项

1. **请求频率**: 建议设置合理的间隔时间，避免过于频繁的请求
2. **Token安全**: 请妥善保管各平台的Token/Key
3. **数据备份**: 定期备份SQLite数据库文件
4. **网络环境**: 确保网络连接稳定
5. **资源占用**: 多个实例同时运行会占用更多系统资源

## 文件结构

```
├── run.py              # 启动脚本
├── main_ui.py          # 主界面
├── database.py         # 数据库操作
├── api_clients.py      # API客户端
├── statistics_window.py # 统计窗口
├── config_manager.py   # 配置管理
├── requirements.txt    # 依赖包
├── config.json         # 配置文件（运行后生成）
├── config_example.json # 示例配置文件
├── install.bat         # 安装脚本
├── start.bat          # 启动脚本
├── task_monitor.db    # SQLite数据库（运行后生成）
├── logs/              # 日志目录（运行后生成）
└── README.md          # 说明文档
```

## 故障排除

### 常见问题

1. **导入模块失败**: 确保已安装所有依赖包
2. **API请求失败**: 检查Token/Key是否正确，网络是否正常
3. **数据库错误**: 检查文件权限，确保有写入权限
4. **界面显示异常**: 确保系统支持CustomTkinter

### 日志查看

程序运行时会在`logs/`目录下生成日志文件，可以查看详细的运行信息和错误信息。

## 技术支持

如有问题，请查看日志文件或联系开发者。
