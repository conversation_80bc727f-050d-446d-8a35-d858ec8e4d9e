#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三叶草平台API客户端
"""

import logging
from typing import Dict, Any

from .base_client import BasePlatformClient

logger = logging.getLogger(__name__)


class CloverClient(BasePlatformClient):
    """三叶草平台API客户端"""
    
    def get_task(self, user_params: Dict[str, str]) -> Dict[str, Any]:
        """
        获取三叶草平台任务
        
        Args:
            user_params: 用户参数，包含uid, sec_uid, type等
            
        Returns:
            任务数据
        """
        try:
            url = f"{self.config.base_url}{self.config.get_task_endpoint}"
            
            # 构建请求参数 - 三叶草平台参数格式
            params = {
                "key": self.credentials.get("key", ""),
                "uid": user_params.get("uid", ""),
                "sec_uid": user_params.get("sec_uid", ""),
                "type": user_params.get("type", "dz")
            }
            
            # 发送GET请求
            response = self._make_request("GET", url, params=params)
            result = response.json()
            
            logger.info(f"🍀 三叶草平台任务获取结果: {result}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 三叶草平台获取任务失败: {e}")
            return self._create_error_response(f"获取任务失败: {str(e)}")
    
    def submit_task(self, task_id: str, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        提交三叶草平台任务结果
        
        Args:
            task_id: 任务ID
            result: 执行结果
            
        Returns:
            提交结果
        """
        try:
            url = f"{self.config.base_url}{self.config.submit_task_endpoint}"
            
            # 构建请求参数 - 三叶草平台参数格式
            params = {
                "key": self.credentials.get("key", ""),
                "uid": result.get("uid", ""),
                "sec_uid": result.get("sec_uid", ""),
                "type": result.get("type", "dz"),
                "task_id": task_id
            }
            
            # 发送GET请求
            response = self._make_request("GET", url, params=params)
            submit_result = response.json()
            
            logger.info(f"🍀 三叶草平台任务提交结果: {submit_result}")
            
            return submit_result
            
        except Exception as e:
            logger.error(f"❌ 三叶草平台提交任务失败: {e}")
            return self._create_error_response(f"提交任务失败: {str(e)}")
    
    def get_api_key(self, account: str, password: str) -> str:
        """
        获取三叶草平台API密钥
        
        Args:
            account: 账号
            password: 密码
            
        Returns:
            API密钥
        """
        try:
            # 三叶草平台获取KEY的API端点
            api_url = "http://www.sanyecao.co:98/keys"
            
            params = {
                "account": account,
                "password": password
            }
            
            response = self._make_request("GET", api_url, params=params)
            result = response.json()
            
            logger.info(f"🍀 三叶草平台KEY获取结果: {result}")
            
            if result.get("code") == 0:
                access_key = result.get("data", {}).get("accesskey", "")
                if access_key:
                    logger.info(f"✅ 三叶草平台KEY获取成功: {access_key}")
                    return access_key
                else:
                    logger.error("❌ 三叶草平台返回的KEY为空")
                    return ""
            else:
                error_msg = result.get("msg", "未知错误")
                logger.error(f"❌ 三叶草平台KEY获取失败: {error_msg}")
                return ""
                
        except Exception as e:
            logger.error(f"❌ 三叶草平台KEY获取异常: {e}")
            return ""
