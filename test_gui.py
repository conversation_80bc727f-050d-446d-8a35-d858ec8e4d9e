#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI测试脚本
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox

# 设置customtkinter主题
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

def test_button_click():
    messagebox.showinfo("测试", "CustomTkinter GUI正常工作！")

def main():
    # 创建主窗口
    root = ctk.CTk()
    root.title("GUI测试")
    root.geometry("400x300")
    
    # 标题
    title_label = ctk.CTkLabel(
        root,
        text="CustomTkinter GUI测试",
        font=ctk.CTkFont(size=20, weight="bold")
    )
    title_label.pack(pady=20)
    
    # 测试按钮
    test_button = ctk.CTkButton(
        root,
        text="点击测试",
        command=test_button_click,
        width=200,
        height=40
    )
    test_button.pack(pady=20)
    
    # 输入框测试
    entry = ctk.CTkEntry(
        root,
        placeholder_text="输入测试文本",
        width=200
    )
    entry.pack(pady=10)
    
    # 下拉框测试
    combo = ctk.CTkComboBox(
        root,
        values=["选项1", "选项2", "选项3"],
        width=200
    )
    combo.pack(pady=10)
    
    # 文本框测试
    text_box = ctk.CTkTextbox(
        root,
        width=300,
        height=100
    )
    text_box.pack(pady=10)
    text_box.insert("0.0", "这是一个测试文本框\n可以输入多行文本")
    
    print("GUI测试窗口已启动")
    print("如果能看到窗口，说明CustomTkinter工作正常")
    
    # 运行主循环
    root.mainloop()

if __name__ == "__main__":
    main()
