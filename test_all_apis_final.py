#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终API测试 - 使用正确的Token
"""

import requests
import json
import time

# 正确的测试数据
TOKENS = {
    "panda": "AKIDa70b4e094e386f6ad010f5ca15e97fd0",
    "clover": "PDb96567483e354c01bf574d3fac0a457a",  # 使用文档中有效的Token
    "sihai": "13ea0139-5be3-4ff8-b140-06a8015a0014"
}

UID = "2159916747338847"
SEC_UID = "MS4wLjABAAAAkgfQBpKUbpa64LtiHKDirZJywNh9Lu1zXHZUOyEXN8lhtDO0eeg0v40_ktynxZ73"

def test_all_platforms():
    """测试所有平台"""
    print("🚀 最终API测试 - 使用正确Token")
    print("=" * 60)
    print(f"UID: {UID}")
    print(f"sec_uid: {SEC_UID}")
    print()
    
    results = {}
    
    # 测试熊猫平台
    print("1️⃣ 测试熊猫平台")
    print("-" * 30)
    panda_result = test_panda()
    results["熊猫平台"] = panda_result
    print()
    
    time.sleep(2)
    
    # 测试三叶草平台
    print("2️⃣ 测试三叶草平台")
    print("-" * 30)
    clover_result = test_clover()
    results["三叶草平台"] = clover_result
    print()
    
    time.sleep(2)
    
    # 测试四海平台
    print("3️⃣ 测试四海平台")
    print("-" * 30)
    sihai_result = test_sihai()
    results["四海平台"] = sihai_result
    print()
    
    # 显示总结
    print("=" * 60)
    print("🎯 测试总结")
    print("=" * 60)
    
    all_success = True
    for platform, result in results.items():
        if result["success"]:
            print(f"✅ {platform}: 成功")
            if result["has_task"]:
                print(f"   📋 获取到任务: {result['task_id']}")
            else:
                print(f"   ℹ️ 暂无任务")
        else:
            print(f"❌ {platform}: 失败 - {result['error']}")
            all_success = False
    
    print()
    if all_success:
        print("🎉 所有平台API测试成功！可以开始使用程序了。")
    else:
        print("⚠️ 部分平台有问题，请检查Token或网络连接。")
    
    return results

def test_panda():
    """测试熊猫平台"""
    try:
        url = "http://112.74.176.127:8020/studio/api/task/get"
        params = {
            "key": TOKENS["panda"],
            "platform": "dy",
            "type": "dz",
            "uid": UID,
            "sec_uid": SEC_UID
        }
        
        response = requests.get(url, params=params, timeout=10, verify=False)
        result = response.json()
        
        if result.get("success") and result.get("code") == 0:
            task_data = result.get("data", {})
            return {
                "success": True,
                "has_task": bool(task_data),
                "task_id": task_data.get("studiotask_id", ""),
                "response": result
            }
        else:
            return {
                "success": False,
                "error": result.get("msg", "未知错误"),
                "response": result
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "response": None
        }

def test_clover():
    """测试三叶草平台"""
    try:
        url = "http://www.sanyecao.co:98/pull"
        params = {
            "key": TOKENS["clover"],
            "uid": UID,
            "sec_uid": SEC_UID,
            "type": "dz"
        }
        
        response = requests.get(url, params=params, timeout=10)
        result = response.json()
        
        if result.get("code") == 0:
            task_data = result.get("data", {})
            return {
                "success": True,
                "has_task": bool(task_data),
                "task_id": task_data.get("task_id", ""),
                "response": result
            }
        else:
            return {
                "success": False,
                "error": result.get("msg", "未知错误"),
                "response": result
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "response": None
        }

def test_sihai():
    """测试四海平台"""
    try:
        url = "http://meetspace.top:2095/order/selectOneTask"
        data = {
            "platform": "2",
            "platformType": "11",
            "uid": UID,
            "uidType": "2"
        }
        
        headers = {
            "Token": TOKENS["sihai"],
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        response = requests.post(url, data=data, headers=headers, timeout=10)
        result = response.json()
        
        if result.get("code") == 1:  # 四海平台成功码是1
            task_data = result.get("data", {})
            return {
                "success": True,
                "has_task": bool(task_data),
                "task_id": task_data.get("taskLogId", ""),
                "response": result
            }
        elif result.get("code") == 0 and "没有获取到任务" in result.get("msg", ""):
            # 暂无任务也算成功
            return {
                "success": True,
                "has_task": False,
                "task_id": "",
                "response": result
            }
        else:
            return {
                "success": False,
                "error": result.get("msg", "未知错误"),
                "response": result
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "response": None
        }

if __name__ == "__main__":
    test_all_platforms()
