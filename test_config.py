#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置功能测试脚本
"""

from config_manager import config_manager
import json

def test_config_manager():
    """测试配置管理器功能"""
    print("=" * 50)
    print("配置管理器功能测试")
    print("=" * 50)
    
    # 测试获取实例配置
    print("\n1. 测试获取实例配置:")
    for i in range(1, 5):
        config = config_manager.get_instance_config(i)
        print(f"实例{i}配置: {config}")
    
    # 测试更新配置
    print("\n2. 测试更新配置:")
    config_manager.update_instance_config(1, "token", "test_token_123")
    config_manager.update_instance_config(1, "platform", "panda")
    config_manager.update_instance_config(1, "uid", "test_uid_456")
    
    updated_config = config_manager.get_instance_config(1)
    print(f"更新后的实例1配置: {updated_config}")
    
    # 测试窗口配置
    print("\n3. 测试窗口配置:")
    window_config = config_manager.get_window_config()
    print(f"窗口配置: {window_config}")
    
    config_manager.update_window_config("geometry", "1600x900")
    updated_window_config = config_manager.get_window_config()
    print(f"更新后的窗口配置: {updated_window_config}")
    
    # 测试数据库配置
    print("\n4. 测试数据库配置:")
    db_config = config_manager.get_database_config()
    print(f"数据库配置: {db_config}")
    
    # 显示完整配置
    print("\n5. 完整配置文件内容:")
    print(json.dumps(config_manager.config_data, ensure_ascii=False, indent=2))
    
    print("\n✅ 配置管理器测试完成！")
    print("配置文件已保存到: config.json")

if __name__ == "__main__":
    test_config_manager()
