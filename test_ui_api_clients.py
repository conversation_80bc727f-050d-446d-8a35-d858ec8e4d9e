#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI中的API客户端是否修复正确
"""

from api_clients import APIClientFactory, parse_task_data, parse_request_data
import json

# 正确的测试数据
TOKENS = {
    "panda": "AKIDa70b4e094e386f6ad010f5ca15e97fd0",
    "clover": "PDb96567483e354c01bf574d3fac0a457a",
    "sihai": "13ea0139-5be3-4ff8-b140-06a8015a0014"
}

UID = "2159916747338847"
SEC_UID = "MS4wLjABAAAAkgfQBpKUbpa64LtiHKDirZJywNh9Lu1zXHZUOyEXN8lhtDO0eeg0v40_ktynxZ73"

def test_ui_api_clients():
    """测试UI中的API客户端"""
    print("🔧 测试修复后的UI API客户端")
    print("=" * 60)
    
    results = {}
    
    # 测试熊猫平台
    print("1️⃣ 测试熊猫平台API客户端")
    print("-" * 40)
    panda_result = test_platform("panda")
    results["熊猫平台"] = panda_result
    print()
    
    # 测试三叶草平台
    print("2️⃣ 测试三叶草平台API客户端")
    print("-" * 40)
    clover_result = test_platform("clover")
    results["三叶草平台"] = clover_result
    print()
    
    # 测试四海平台
    print("3️⃣ 测试四海平台API客户端")
    print("-" * 40)
    sihai_result = test_platform("sihai")
    results["四海平台"] = sihai_result
    print()
    
    # 显示总结
    print("=" * 60)
    print("🎯 UI API客户端测试总结")
    print("=" * 60)
    
    all_success = True
    for platform, result in results.items():
        if result["api_success"]:
            print(f"✅ {platform}: API调用成功")
            if result["has_task"]:
                print(f"   📋 获取到任务: {result['task_id']}")
                print(f"   💾 数据解析: {'成功' if result['parse_success'] else '失败'}")
            else:
                print(f"   ℹ️ 暂无任务")
        else:
            print(f"❌ {platform}: API调用失败 - {result['error']}")
            all_success = False
    
    print()
    if all_success:
        print("🎉 UI API客户端修复成功！所有平台都能正常工作。")
    else:
        print("⚠️ 部分平台仍有问题，需要进一步检查。")
    
    return results

def test_platform(platform):
    """测试指定平台"""
    try:
        # 创建API客户端
        if platform == "sihai":
            credentials = {"token": TOKENS[platform]}
        else:
            credentials = {"key": TOKENS[platform]}
        
        client = APIClientFactory.create_client(platform, credentials)
        if not client:
            return {
                "api_success": False,
                "error": "无法创建API客户端",
                "has_task": False,
                "parse_success": False
            }
        
        # 构建请求参数
        user_params = {
            "uid": UID,
            "sec_uid": SEC_UID,
            "platform": "dy",
            "type": "dz"
        }
        
        print(f"使用Token: {TOKENS[platform]}")
        print(f"请求参数: {user_params}")
        
        # 发送请求
        response = client.get_task(user_params)
        print(f"API响应: {json.dumps(response, ensure_ascii=False, indent=2)}")
        
        # 解析请求数据
        request_data = parse_request_data(platform, response, UID, SEC_UID)
        print(f"请求数据解析: {request_data['success']}")
        
        # 检查是否有任务
        has_task = request_data["has_task"]
        task_id = request_data["task_id"]
        
        parse_success = False
        if has_task:
            # 解析任务数据
            task_data = parse_task_data(platform, response, UID)
            parse_success = task_data is not None
            if parse_success:
                print(f"任务数据解析成功: {task_data['task_id']}")
            else:
                print("任务数据解析失败")
        
        client.close()
        
        return {
            "api_success": True,
            "has_task": has_task,
            "task_id": task_id,
            "parse_success": parse_success,
            "response": response
        }
        
    except Exception as e:
        print(f"测试异常: {e}")
        return {
            "api_success": False,
            "error": str(e),
            "has_task": False,
            "parse_success": False
        }

if __name__ == "__main__":
    test_ui_api_clients()
