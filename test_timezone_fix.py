#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试时区修复
"""

from database import DatabaseManager
from datetime import datetime, timezone, timedelta
import sqlite3

def test_timezone_fix():
    """测试时区修复"""
    print("🕐 测试时区修复")
    print("=" * 60)
    
    # 创建测试数据库
    db = DatabaseManager("test_timezone.db")
    
    # 插入测试数据
    test_request_data = {
        "platform": "test",
        "request_type": "get_task",
        "sent_uid": "123456",
        "sec_uid": "test_sec_uid",
        "success": True,
        "response_code": 200,
        "response_message": "success",
        "has_task": True,
        "task_id": "test_task_123",
        "raw_response": '{"test": "data"}'
    }
    
    print("1️⃣ 插入测试数据")
    request_id = db.insert_request(test_request_data)
    print(f"✅ 插入请求记录，ID: {request_id}")
    
    # 查询数据库中的时间
    print("\n2️⃣ 检查数据库中的时间")
    with db.get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT created_at FROM requests WHERE id = ?", (request_id,))
        db_time = cursor.fetchone()[0]
        print(f"数据库中的时间: {db_time}")
    
    # 获取当前本地时间
    local_now = datetime.now()
    print(f"当前本地时间: {local_now.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 解析数据库时间
    try:
        db_datetime = datetime.fromisoformat(db_time)
        print(f"解析后的时间: {db_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 计算时间差
        time_diff = abs((local_now - db_datetime).total_seconds())
        print(f"时间差: {time_diff:.1f} 秒")
        
        if time_diff < 60:  # 1分钟内认为正常
            print("✅ 时区修复成功！数据库存储的是本地时间")
        else:
            print("⚠️ 时间差较大，可能仍有时区问题")
            
    except Exception as e:
        print(f"❌ 时间解析失败: {e}")
    
    # 测试统计查询
    print("\n3️⃣ 测试统计查询")
    try:
        summary = db.get_platform_summary()
        if summary:
            for row in summary:
                print(f"平台: {row['platform']}")
                print(f"首次请求: {row['first_request']}")
                print(f"最后请求: {row['last_request']}")
        else:
            print("暂无统计数据")
    except Exception as e:
        print(f"❌ 统计查询失败: {e}")
    
    # 清理测试数据库
    print("\n4️⃣ 清理测试数据")
    try:
        import os
        if os.path.exists("test_timezone.db"):
            os.remove("test_timezone.db")
            print("✅ 测试数据库已清理")
    except Exception as e:
        print(f"⚠️ 清理测试数据库失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 时区修复测试完成")
    print("=" * 60)

def test_old_vs_new_database():
    """对比新旧数据库的时间存储"""
    print("\n🔄 对比新旧数据库时间存储")
    print("-" * 40)
    
    # 测试旧的CURRENT_TIMESTAMP
    print("旧方式 (CURRENT_TIMESTAMP):")
    conn_old = sqlite3.connect(":memory:")
    cursor_old = conn_old.cursor()
    cursor_old.execute('''
        CREATE TABLE test_old (
            id INTEGER PRIMARY KEY,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    cursor_old.execute("INSERT INTO test_old DEFAULT VALUES")
    cursor_old.execute("SELECT created_at FROM test_old")
    old_time = cursor_old.fetchone()[0]
    print(f"  存储时间: {old_time}")
    conn_old.close()
    
    # 测试新的localtime
    print("\n新方式 (datetime('now', 'localtime')):")
    conn_new = sqlite3.connect(":memory:")
    cursor_new = conn_new.cursor()
    cursor_new.execute('''
        CREATE TABLE test_new (
            id INTEGER PRIMARY KEY,
            created_at TIMESTAMP DEFAULT (datetime('now', 'localtime'))
        )
    ''')
    cursor_new.execute("INSERT INTO test_new DEFAULT VALUES")
    cursor_new.execute("SELECT created_at FROM test_new")
    new_time = cursor_new.fetchone()[0]
    print(f"  存储时间: {new_time}")
    conn_new.close()
    
    # 当前本地时间
    local_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"\n当前本地时间: {local_time}")
    
    # 分析差异
    try:
        old_dt = datetime.fromisoformat(old_time)
        new_dt = datetime.fromisoformat(new_time)
        local_dt = datetime.now()
        
        old_diff = abs((local_dt - old_dt).total_seconds())
        new_diff = abs((local_dt - new_dt).total_seconds())
        
        print(f"\n时间差分析:")
        print(f"  旧方式差异: {old_diff:.1f} 秒")
        print(f"  新方式差异: {new_diff:.1f} 秒")
        
        if new_diff < old_diff:
            print("✅ 新方式更接近本地时间")
        else:
            print("⚠️ 需要进一步检查")
            
    except Exception as e:
        print(f"❌ 时间差分析失败: {e}")

if __name__ == "__main__":
    test_timezone_fix()
    test_old_vs_new_database()
