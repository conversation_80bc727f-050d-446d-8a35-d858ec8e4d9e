#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
熊猫平台API客户端
"""

import logging
from typing import Dict, Any

from .base_client import BasePlatformClient

logger = logging.getLogger(__name__)


class PandaClient(BasePlatformClient):
    """熊猫平台API客户端"""
    
    def get_task(self, user_params: Dict[str, str]) -> Dict[str, Any]:
        """
        获取熊猫平台任务
        
        Args:
            user_params: 用户参数，包含uid, sec_uid, platform, type等
            
        Returns:
            任务数据
        """
        try:
            url = f"{self.config.base_url}{self.config.get_task_endpoint}"
            
            # 构建请求参数
            params = {
                "key": self.credentials.get("key", ""),
                **user_params
            }
            
            # 发送GET请求
            response = self._make_request("GET", url, params=params, verify=False)
            result = response.json()
            
            logger.info(f"🐼 熊猫平台任务获取结果: {result}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 熊猫平台获取任务失败: {e}")
            return self._create_error_response(f"获取任务失败: {str(e)}")
    
    def submit_task(self, task_id: str, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        提交熊猫平台任务结果
        
        Args:
            task_id: 任务ID (studiotask_id)
            result: 执行结果
            
        Returns:
            提交结果
        """
        try:
            url = f"{self.config.base_url}{self.config.submit_task_endpoint}"
            
            # 构建请求参数
            params = {
                "key": self.credentials.get("key", ""),
                "platform": result.get("platform", "dy"),
                "type": result.get("type", "dz"),
                "studiotask_id": task_id
            }
            
            # 发送GET请求
            response = self._make_request("GET", url, params=params, verify=False)
            submit_result = response.json()
            
            logger.info(f"🐼 熊猫平台任务提交结果: {submit_result}")
            
            return submit_result
            
        except Exception as e:
            logger.error(f"❌ 熊猫平台提交任务失败: {e}")
            return self._create_error_response(f"提交任务失败: {str(e)}")
    
    def get_api_key(self, account: str, password: str) -> str:
        """
        获取熊猫平台API密钥
        
        Args:
            account: 账号
            password: 密码
            
        Returns:
            API密钥
        """
        try:
            # 熊猫平台获取KEY的API端点
            api_url = "http://112.74.176.127:8020/apikey"
            
            params = {
                "account": account,
                "pwd": password
            }
            
            response = self._make_request("GET", api_url, params=params)
            result = response.json()
            
            logger.info(f"🐼 熊猫平台KEY获取结果: {result}")
            
            if result.get("success") and result.get("code") == 0:
                access_key = result.get("data", {}).get("accesskey", "")
                if access_key:
                    logger.info(f"✅ 熊猫平台KEY获取成功: {access_key}")
                    return access_key
                else:
                    logger.error("❌ 熊猫平台返回的KEY为空")
                    return ""
            else:
                error_msg = result.get("msg", "未知错误")
                logger.error(f"❌ 熊猫平台KEY获取失败: {error_msg}")
                return ""
                
        except Exception as e:
            logger.error(f"❌ 熊猫平台KEY获取异常: {e}")
            return ""
