@echo off
echo ========================================
echo 任务平台API测试工具 - 安装脚本
echo ========================================

echo 正在安装Python依赖包...
pip install -r requirements.txt

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo 安装完成！
    echo ========================================
    echo 运行命令: python run.py
    echo.
    pause
) else (
    echo.
    echo ========================================
    echo 安装失败！请检查Python环境
    echo ========================================
    pause
)
