# 熊猫平台
## 获取任务
发包示例：
curl -X GET 'http://**************:8020/studio/api/task/get?key=AKID99fd081a6d4cb407309dd1aa788f3040&platform=dy&type=dz&uid=94342769630&sec_uid=MS4wLjABAAAADJoQK7gB4bioMeVJIMcF2DlwXZtnn8sV94ANZzYtdWg' -H 'Host: **************:8020' -H 'User-Agent: Go-http-client/1.1' -H 'Accept-Encoding: gzip' --http1.1

正常返回结果示例：
{
  "success": true,
  "code": 0,
  "data": {
    "studiotask_id": "1947470341668343808",
    "type": "dz",
    "params": {
      "price": 165,
      "sec_uid": "MS4wLjABAAAAm9h1nMYoXSKXim_gOiBJ1c1tbgJ508YkxFr85-Rmo_E",
      "share_url": "https://v.douyin.com/pzaqN5eSxdQ/",
      "uid": "88976807900",
      "video_id": "7496830076384038198"
    }
  },
  "msg": "success"
}

无任务示例：
{
  "success": false,
  "code": 406,
  "data": {},
  "msg": "无任务"
}

访问频繁示例：
{
  "success": false,
  "code": 403,
  "data": {},
  "msg": "访问频繁"
}

## 获取任务结果
发包示例：
curl -X GET 'http://**************:8020/studio/api/task/submit?platform=dy&type=dz&studiotask_id=1947470365328412672&key=AKID99fd081a6d4cb407309dd1aa788f3040' -H 'Host: **************:8020' -H 'User-Agent: Go-http-client/1.1' -H 'Accept-Encoding: gzip' --http1.1

结果示例：
{
  "success": true,
  "code": 0,
  "data": null,
  "msg": ""
}

# 三叶草平台
## 获取任务
发包示例：
curl -X GET 'http://www.sanyecao.co:98/pull?key=PDb96567483e354c01bf574d3fac0a457a&uid=2159916747338847&sec_uid=MS4wLjABAAAAkgfQBpKUbpa64LtiHKDirZJywNh9Lu1zXHZUOyEXN8lhtDO0eeg0v40_ktynxZ73&type=dz' -H 'Host: www.sanyecao.co:98' -H 'User-Agent: Go-http-client/1.1' -H 'Accept-Encoding: gzip' --http1.1

正常返回结果示例：
{
  "code": 0,
  "msg": "success",
  "data": {
    "task_id": "1946223005573550081",
    "task_type": "sp",
    "uid": "989981196949111",
    "sec_uid": "MS4wLjABAAAAdCTk6iS6EMPzHfqhV0zEFw9AVqKNaRUY-nvR-ixOjEg",
    "video_id": "7527867997491334451",
    "share_url": "https://v.douyin.com/sxf_tQI1DW4/"
  }
}

## 获取任务结果
发包示例：
curl -X GET 'http://www.sanyecao.co:98/push?key=PDb96567483e354c01bf574d3fac0a457a&uid=2159916747338847&sec_uid=MS4wLjABAAAAkgfQBpKUbpa64LtiHKDirZJywNh9Lu1zXHZUOyEXN8lhtDO0eeg0v40_ktynxZ73&type=dz&task_id=1946958815653326850' -H 'Host: www.sanyecao.co:98' -H 'User-Agent: Go-http-client/1.1' -H 'Accept-Encoding: gzip' --http1.1
结果示例：
{
  "code": 0,
  "msg": "success",
  "data": null
}

# 四海平台
## 获取任务
发包示例：
curl -X POST 'http://meetspace.top:2095/order/selectOneTask' -H 'Host: meetspace.top:2095' -H 'User-Agent: Go-http-client/1.1' -H 'Accept-Encoding: gzip' -H 'Content-Type: application/x-www-form-urlencoded' -H 'Token: 13ea0139-5be3-4ff8-b140-06a8015a0014' --data-urlencode 'platform=2' --data-urlencode 'platformType=11' --data-urlencode 'uid=2159916747338847' --data-urlencode 'uidType=2' --http1.1

正常返回结果示例：
{
  "msg": "成功",
  "code": 1,
  "data": {
    "taskLogId": "af252db46cc0",
    "uid": "916306859393929",
    "platformTypeName": "点赞",
    "authorUserId": "916306859393929",
    "secUid": "MS4wLjABAAAAeN26t8I9xdR7ltvsdyv3E5QipyOzcuURyqx7LodUVyg",
    "shortUrl": "https://www.iesdouyin.com/share/note/7500996434615864602/?region=CN&amp;mid=7197742028636359485&amp;u_code=m2gk1dc2c76&amp;did=MS4wLjABAAAAjxQ2LonswXdSfJSiDXyRLjUuOCISXk0vSEbGXW3JMzsAwjfGZyCEWeD4EGmPAwaC&amp;iid=MS4wLjABAAAAg576JHfex-0SNRD9_MlkX_rRJ14GK0AVMq2VGTCxBmjwwC8i1PVRTu9tT9kVxeN_&amp;with_sec_did=1&amp;video_share_track_ver=&amp;titleType=title&amp;schema_type=37&amp;share_sign=2ig8d5A_R6oZXuRnZUT9dun35BajM03.fU.PWjea7CQ-&amp;share_version=340500&amp;ts=1752815799&amp;from_aid=1128&amp;from_ssr=1",
    "videoType": "2",
    "douyinUrl": "https://www.iesdouyin.com/share/note/7500996434615864602/?region=CN&amp;mid=7197742028636359485&amp;u_code=m2gk1dc2c76&amp;did=MS4wLjABAAAAjxQ2LonswXdSfJSiDXyRLjUuOCISXk0vSEbGXW3JMzsAwjfGZyCEWeD4EGmPAwaC&amp;iid=MS4wLjABAAAAg576JHfex-0SNRD9_MlkX_rRJ14GK0AVMq2VGTCxBmjwwC8i1PVRTu9tT9kVxeN_&amp;with_sec_did=1&amp;video_share_track_ver=&amp;titleType=title&amp;schema_type=37&amp;share_sign=2ig8d5A_R6oZXuRnZUT9dun35BajM03.fU.PWjea7CQ-&amp;share_version=340500&amp;ts=1752815799&amp;from_aid=1128&amp;from_ssr=1",
    "orderReceivePrice": "0.01250000",
    "nickname": "y",
    "platformName": "痘印",
    "video_id": "7500996434615864602?previous_page=app_code_link\">https://www.douyin.com/note/7500996434615864602?previous_page=app_code_link</a>."
  }
}

## 获取任务结果
发包示例：
curl -X POST 'http://meetspace.top:2095/order/taskSubmit' -H 'Host: meetspace.top:2095' -H 'User-Agent: Go-http-client/1.1' -H 'Accept-Encoding: gzip' -H 'Content-Type: application/x-www-form-urlencoded' -H 'Token: 13ea0139-5be3-4ff8-b140-06a8015a0014' --data-urlencode 'taskLogId=eb31e5c5bc33' --data-urlencode 'status=1' --http1.1

{
  "msg": "提交成功",
  "code": 1
}

