#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
"""

import json
import os
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config_data = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置数据字典
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"✅ 配置文件加载成功: {self.config_file}")
                return config
            else:
                logger.info(f"📝 配置文件不存在，创建默认配置: {self.config_file}")
                return self.create_default_config()
        except Exception as e:
            logger.error(f"❌ 加载配置文件失败: {e}")
            return self.create_default_config()
    
    def create_default_config(self) -> Dict[str, Any]:
        """
        创建默认配置
        
        Returns:
            默认配置字典
        """
        default_config = {
            "instances": {
                "1": {
                    "token": "",
                    "platform": "panda",
                    "uid": "",
                    "sec_uid": "",
                    "interval": "10"
                },
                "2": {
                    "token": "",
                    "platform": "clover",
                    "uid": "",
                    "sec_uid": "",
                    "interval": "10"
                },
                "3": {
                    "token": "",
                    "platform": "sihai",
                    "uid": "",
                    "sec_uid": "",
                    "interval": "10"
                },
                "4": {
                    "token": "",
                    "platform": "panda",
                    "uid": "",
                    "sec_uid": "",
                    "interval": "10"
                }
            },
            "window": {
                "geometry": "1600x900",
                "theme": "dark"
            },
            "database": {
                "auto_cleanup_days": 30,
                "backup_enabled": False
            }
        }
        
        # 保存默认配置
        self.save_config(default_config)
        return default_config
    
    def save_config(self, config_data: Optional[Dict[str, Any]] = None):
        """
        保存配置到文件
        
        Args:
            config_data: 要保存的配置数据，None则保存当前配置
        """
        try:
            data_to_save = config_data if config_data is not None else self.config_data
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=2)
            
            if config_data is not None:
                self.config_data = data_to_save
            
            logger.debug(f"✅ 配置已保存: {self.config_file}")
            
        except Exception as e:
            logger.error(f"❌ 保存配置文件失败: {e}")
    
    def get_instance_config(self, instance_id: int) -> Dict[str, str]:
        """
        获取指定实例的配置
        
        Args:
            instance_id: 实例ID
            
        Returns:
            实例配置字典
        """
        instance_key = str(instance_id)
        return self.config_data.get("instances", {}).get(instance_key, {
            "token": "",
            "platform": "panda",
            "uid": "",
            "sec_uid": "",
            "interval": "10"
        })
    
    def update_instance_config(self, instance_id: int, field: str, value: str):
        """
        更新指定实例的配置字段
        
        Args:
            instance_id: 实例ID
            field: 字段名
            value: 字段值
        """
        instance_key = str(instance_id)
        
        # 确保实例配置存在
        if "instances" not in self.config_data:
            self.config_data["instances"] = {}
        
        if instance_key not in self.config_data["instances"]:
            self.config_data["instances"][instance_key] = {
                "token": "",
                "platform": "panda",
                "uid": "",
                "sec_uid": "",
                "interval": "10"
            }
        
        # 更新字段值
        self.config_data["instances"][instance_key][field] = value
        
        # 自动保存
        self.save_config()
        
        logger.debug(f"📝 实例{instance_id}配置已更新: {field} = {value}")
    
    def get_window_config(self) -> Dict[str, str]:
        """
        获取窗口配置
        
        Returns:
            窗口配置字典
        """
        return self.config_data.get("window", {
            "geometry": "1600x900",
            "theme": "dark"
        })
    
    def update_window_config(self, field: str, value: str):
        """
        更新窗口配置
        
        Args:
            field: 字段名
            value: 字段值
        """
        if "window" not in self.config_data:
            self.config_data["window"] = {}
        
        self.config_data["window"][field] = value
        self.save_config()
        
        logger.debug(f"📝 窗口配置已更新: {field} = {value}")
    
    def get_database_config(self) -> Dict[str, Any]:
        """
        获取数据库配置
        
        Returns:
            数据库配置字典
        """
        return self.config_data.get("database", {
            "auto_cleanup_days": 30,
            "backup_enabled": False
        })
    
    def export_config(self, export_file: str) -> bool:
        """
        导出配置到指定文件
        
        Args:
            export_file: 导出文件路径
            
        Returns:
            是否导出成功
        """
        try:
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ 配置已导出到: {export_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 导出配置失败: {e}")
            return False
    
    def import_config(self, import_file: str) -> bool:
        """
        从指定文件导入配置
        
        Args:
            import_file: 导入文件路径
            
        Returns:
            是否导入成功
        """
        try:
            if not os.path.exists(import_file):
                logger.error(f"❌ 导入文件不存在: {import_file}")
                return False
            
            with open(import_file, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 验证配置格式
            if not isinstance(imported_config, dict):
                logger.error("❌ 导入的配置格式无效")
                return False
            
            # 备份当前配置
            backup_file = f"{self.config_file}.backup"
            self.export_config(backup_file)
            
            # 应用导入的配置
            self.config_data = imported_config
            self.save_config()
            
            logger.info(f"✅ 配置已从 {import_file} 导入成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 导入配置失败: {e}")
            return False
    
    def reset_config(self):
        """重置配置为默认值"""
        self.config_data = self.create_default_config()
        logger.info("✅ 配置已重置为默认值")


# 全局配置管理器实例
config_manager = ConfigManager()
