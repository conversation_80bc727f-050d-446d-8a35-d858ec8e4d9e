#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API客户端模块
"""

import requests
import json
import logging
from typing import Dict, Any, Optional
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class BaseAPIClient(ABC):
    """API客户端基类"""
    
    def __init__(self, credentials: Dict[str, str]):
        """
        初始化客户端
        
        Args:
            credentials: 认证信息
        """
        self.credentials = credentials
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Go-http-client/1.1',
            'Accept-Encoding': 'gzip'
        })
    
    @abstractmethod
    def get_task(self, user_params: Dict[str, str]) -> Dict[str, Any]:
        """获取任务"""
        pass
    
    def close(self):
        """关闭会话"""
        if self.session:
            self.session.close()


class PandaAPIClient(BaseAPIClient):
    """熊猫平台API客户端"""
    
    BASE_URL = "http://**************:8020/studio/api/task/get"
    
    def get_task(self, user_params: Dict[str, str]) -> Dict[str, Any]:
        """
        获取熊猫平台任务
        
        Args:
            user_params: 用户参数 {uid, sec_uid, platform, type}
            
        Returns:
            任务数据
        """
        try:
            params = {
                "key": self.credentials.get("key", ""),
                "platform": user_params.get("platform", "dy"),
                "type": user_params.get("type", "dz"),
                "uid": user_params.get("uid", ""),
                "sec_uid": user_params.get("sec_uid", "")
            }
            
            # 设置请求头
            headers = {
                'Host': '**************:8020',
                'User-Agent': 'Go-http-client/1.1',
                'Accept-Encoding': 'gzip'
            }

            logger.info(f"🐼 熊猫平台请求参数: {params}")

            response = self.session.get(
                self.BASE_URL,
                params=params,
                headers=headers,
                timeout=10,
                verify=False
            )
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"🐼 熊猫平台响应: {result}")
            
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 熊猫平台请求失败: {e}")
            return {
                "success": False,
                "code": 500,
                "data": {},
                "msg": f"请求失败: {str(e)}"
            }
        except json.JSONDecodeError as e:
            logger.error(f"❌ 熊猫平台响应解析失败: {e}")
            return {
                "success": False,
                "code": 500,
                "data": {},
                "msg": f"响应解析失败: {str(e)}"
            }


class CloverAPIClient(BaseAPIClient):
    """三叶草平台API客户端"""
    
    BASE_URL = "http://www.sanyecao.co:98/pull"
    
    def get_task(self, user_params: Dict[str, str]) -> Dict[str, Any]:
        """
        获取三叶草平台任务
        
        Args:
            user_params: 用户参数 {uid, sec_uid, type}
            
        Returns:
            任务数据
        """
        try:
            params = {
                "key": self.credentials.get("key", ""),
                "uid": user_params.get("uid", ""),
                "sec_uid": user_params.get("sec_uid", ""),
                "type": user_params.get("type", "dz")
            }
            
            # 设置请求头
            headers = {
                'Host': 'www.sanyecao.co:98',
                'User-Agent': 'Go-http-client/1.1',
                'Accept-Encoding': 'gzip'
            }

            logger.info(f"🍀 三叶草平台请求参数: {params}")

            response = self.session.get(
                self.BASE_URL,
                params=params,
                headers=headers,
                timeout=10
            )
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"🍀 三叶草平台响应: {result}")
            
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 三叶草平台请求失败: {e}")
            return {
                "code": 500,
                "msg": f"请求失败: {str(e)}",
                "data": {}
            }
        except json.JSONDecodeError as e:
            logger.error(f"❌ 三叶草平台响应解析失败: {e}")
            return {
                "code": 500,
                "msg": f"响应解析失败: {str(e)}",
                "data": {}
            }


class SihaiAPIClient(BaseAPIClient):
    """四海平台API客户端"""
    
    BASE_URL = "http://meetspace.top:2095/order/selectOneTask"
    
    def get_task(self, user_params: Dict[str, str]) -> Dict[str, Any]:
        """
        获取四海平台任务
        
        Args:
            user_params: 用户参数 {uid}
            
        Returns:
            任务数据
        """
        try:
            data = {
                "platform": "2",
                "platformType": "11",  # 点赞类型
                "uid": user_params.get("uid", ""),
                "uidType": "2"
            }
            
            headers = {
                "Token": self.credentials.get("token", ""),
                "Content-Type": "application/x-www-form-urlencoded"
            }
            
            logger.info(f"🌊 四海平台请求数据: {data}")
            logger.info(f"🌊 四海平台请求头: {headers}")
            
            response = self.session.post(
                self.BASE_URL, 
                data=data, 
                headers=headers,
                timeout=10
            )
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"🌊 四海平台响应: {result}")
            
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 四海平台请求失败: {e}")
            return {
                "code": 500,
                "msg": f"请求失败: {str(e)}",
                "data": {}
            }
        except json.JSONDecodeError as e:
            logger.error(f"❌ 四海平台响应解析失败: {e}")
            return {
                "code": 500,
                "msg": f"响应解析失败: {str(e)}",
                "data": {}
            }


class APIClientFactory:
    """API客户端工厂"""
    
    @staticmethod
    def create_client(platform: str, credentials: Dict[str, str]) -> Optional[BaseAPIClient]:
        """
        创建API客户端
        
        Args:
            platform: 平台名称 (panda, clover, sihai)
            credentials: 认证信息
            
        Returns:
            API客户端实例
        """
        if platform == "panda":
            return PandaAPIClient(credentials)
        elif platform == "clover":
            return CloverAPIClient(credentials)
        elif platform == "sihai":
            return SihaiAPIClient(credentials)
        else:
            logger.error(f"❌ 不支持的平台: {platform}")
            return None


def parse_task_data(platform: str, raw_data: Dict[str, Any], sent_uid: str) -> Optional[Dict[str, Any]]:
    """
    解析任务数据
    
    Args:
        platform: 平台名称
        raw_data: 原始响应数据
        sent_uid: 发送的UID
        
    Returns:
        解析后的任务数据
    """
    try:
        if platform == "panda":
            if not raw_data.get("success") or raw_data.get("code") != 0:
                return None
            
            data = raw_data.get("data", {})
            params = data.get("params", {})
            
            return {
                "task_id": str(data.get("studiotask_id", "")),
                "platform": str(platform),
                "sent_uid": str(sent_uid),
                "received_vid": str(params.get("video_id", "")),
                "sec_uid": str(params.get("sec_uid", "")),
                "share_url": str(params.get("share_url", "")),
                "price": float(params.get("price", 0)) / 100,  # 分转元
                "task_type": str(data.get("type", "")),
                "nickname": str(""),
                "raw_data": json.dumps(raw_data, ensure_ascii=False)
            }
            
        elif platform == "clover":
            if raw_data.get("code") != 0:
                return None
            
            data = raw_data.get("data", {})
            
            return {
                "task_id": str(data.get("task_id", "")),
                "platform": str(platform),
                "sent_uid": str(sent_uid),
                "received_vid": str(data.get("video_id", "")),
                "sec_uid": str(data.get("sec_uid", "")),
                "share_url": str(data.get("share_url", "")),
                "price": 1.0,  # 默认价格
                "task_type": str(data.get("task_type", "")),
                "nickname": str(""),
                "raw_data": json.dumps(raw_data, ensure_ascii=False)
            }
            
        elif platform == "sihai":
            if raw_data.get("code") != 1:
                return None
            
            data = raw_data.get("data", {})
            
            return {
                "task_id": str(data.get("taskLogId", "")),
                "platform": str(platform),
                "sent_uid": str(sent_uid),
                "received_vid": str(data.get("video_id", "").split("?")[0] if data.get("video_id") else ""),
                "sec_uid": str(data.get("secUid", "")),
                "share_url": str(data.get("shortUrl", "") or data.get("douyinUrl", "")),
                "price": round(float(data.get("orderReceivePrice", 0)) * 100, 2),
                "task_type": str("点赞"),
                "nickname": str(data.get("nickname", "")),
                "raw_data": json.dumps(raw_data, ensure_ascii=False)
            }
            
        return None
        
    except Exception as e:
        logger.error(f"❌ 解析{platform}平台任务数据失败: {e}")
        return None


def parse_request_data(platform: str, raw_data: Dict[str, Any], sent_uid: str, sec_uid: str = "") -> Dict[str, Any]:
    """
    解析请求数据
    
    Args:
        platform: 平台名称
        raw_data: 原始响应数据
        sent_uid: 发送的UID
        sec_uid: sec_uid
        
    Returns:
        解析后的请求数据
    """
    try:
        if platform == "panda":
            success = raw_data.get("success", False)
            code = raw_data.get("code", 0)
            msg = raw_data.get("msg", "")
            has_task = success and code == 0 and raw_data.get("data", {})
            task_id = raw_data.get("data", {}).get("studiotask_id", "") if has_task else ""
            
        elif platform == "clover":
            code = raw_data.get("code", 0)
            success = code == 0
            msg = raw_data.get("msg", "")
            has_task = success and raw_data.get("data", {})
            task_id = raw_data.get("data", {}).get("task_id", "") if has_task else ""
            
        elif platform == "sihai":
            code = raw_data.get("code", 0)
            success = code == 1  # 四海平台成功码是1
            msg = raw_data.get("msg", "")
            has_task = success and raw_data.get("data", {})
            task_id = raw_data.get("data", {}).get("taskLogId", "") if has_task else ""
            
        else:
            success = False
            code = 500
            msg = f"不支持的平台: {platform}"
            has_task = False
            task_id = ""
        
        return {
            "platform": str(platform),
            "request_type": str("get_task"),
            "sent_uid": str(sent_uid),
            "sec_uid": str(sec_uid),
            "success": bool(success),
            "response_code": int(code),
            "response_message": str(msg),
            "has_task": bool(has_task),
            "task_id": str(task_id),
            "raw_response": json.dumps(raw_data, ensure_ascii=False)
        }
        
    except Exception as e:
        logger.error(f"❌ 解析{platform}平台请求数据失败: {e}")
        return {
            "platform": str(platform),
            "request_type": str("get_task"),
            "sent_uid": str(sent_uid),
            "sec_uid": str(sec_uid),
            "success": bool(False),
            "response_code": int(500),
            "response_message": str(f"解析失败: {str(e)}"),
            "has_task": bool(False),
            "task_id": str(""),
            "raw_response": json.dumps(raw_data, ensure_ascii=False)
        }
