#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务平台测试工具启动脚本
"""

import sys
import os
import logging
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from main_ui import MainApplication
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保已安装所有依赖包:")
    print("pip install -r requirements.txt")
    sys.exit(1)


def setup_logging():
    """设置日志配置"""
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"task_monitor_{datetime.now().strftime('%Y%m%d')}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )


def main():
    """主函数"""
    print("=" * 50)
    print("任务平台API测试工具")
    print("=" * 50)
    print("支持平台: 熊猫、三叶草、四海")
    print("功能: API测试、数据记录、统计分析")
    print("=" * 50)
    
    # 设置日志
    setup_logging()
    
    try:
        # 创建并运行应用
        app = MainApplication()
        app.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        logging.error(f"程序运行异常: {e}")
        print(f"程序运行异常: {e}")
    finally:
        print("程序已退出")


if __name__ == "__main__":
    main()
