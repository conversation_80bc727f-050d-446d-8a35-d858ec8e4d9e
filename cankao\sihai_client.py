#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四海平台API客户端
"""

import logging
from typing import Dict, Any

from .base_client import BasePlatformClient

logger = logging.getLogger(__name__)


class SihaiClient(BasePlatformClient):
    """四海平台API客户端"""
    
    def get_task(self, user_params: Dict[str, str]) -> Dict[str, Any]:
        """
        获取四海平台任务
        
        Args:
            user_params: 用户参数，包含uid等
            
        Returns:
            任务数据
        """
        try:
            url = f"{self.config.base_url}{self.config.get_task_endpoint}"
            
            # 构建请求数据 - 四海平台使用POST请求
            data = {
                "platform": "2",
                "platformType": "11",  # 点赞类型
                "uid": user_params.get("uid", ""),
                "uidType": "2"
            }
            
            # 设置请求头
            headers = {
                "Host": "meetspace.top:2095",
                "User-Agent": "Go-http-client/1.1",
                "Accept-Encoding": "gzip",
                "Token": self.credentials.get("token", ""),
                "Content-Type": "application/x-www-form-urlencoded"
            }
            
            # 发送POST请求
            response = self._make_request("POST", url, data=data, headers=headers)
            result = response.json()
            
            logger.info(f"🌊 四海平台任务获取结果: {result}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 四海平台获取任务失败: {e}")
            return self._create_error_response(f"获取任务失败: {str(e)}")
    
    def submit_task(self, task_id: str, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        提交四海平台任务结果
        
        Args:
            task_id: 任务ID (taskLogId)
            result: 执行结果
            
        Returns:
            提交结果
        """
        try:
            url = f"{self.config.base_url}{self.config.submit_task_endpoint}"
            
            # 构建请求数据 - 四海平台使用POST请求
            data = {
                "taskLogId": task_id,
                "status": "1"  # 1表示成功
            }
            
            # 设置请求头
            headers = {
                "Host": "meetspace.top:2095",
                "User-Agent": "Go-http-client/1.1",
                "Accept-Encoding": "gzip",
                "Token": self.credentials.get("token", ""),
                "Content-Type": "application/x-www-form-urlencoded"
            }
            
            # 发送POST请求
            response = self._make_request("POST", url, data=data, headers=headers)
            submit_result = response.json()
            
            logger.info(f"🌊 四海平台任务提交结果: {submit_result}")
            
            return submit_result
            
        except Exception as e:
            logger.error(f"❌ 四海平台提交任务失败: {e}")
            return self._create_error_response(f"提交任务失败: {str(e)}")
    
    def get_api_key(self, account: str, password: str) -> str:
        """
        获取四海平台API密钥(Token)
        
        Args:
            account: 账号
            password: 密码
            
        Returns:
            API Token
        """
        try:
            # 四海平台登录获取Token的API端点
            api_url = "http://meetspace.top:2095/order/login"
            
            data = {
                "username": account,
                "password": password
            }
            
            headers = {
                'Host': 'meetspace.top:2095',
                'User-Agent': 'Go-http-client/1.1',
                'Accept-Encoding': 'gzip',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            response = self._make_request("POST", api_url, data=data, headers=headers)
            result = response.json()
            
            logger.info(f"🌊 四海平台Token获取结果: {result}")
            
            if result.get("code") == 1:  # 四海平台成功码是1
                access_token = result.get("token", "")
                if access_token:
                    logger.info(f"✅ 四海平台Token获取成功: {access_token}")
                    return access_token
                else:
                    logger.error("❌ 四海平台返回的Token为空")
                    return ""
            else:
                error_msg = result.get("msg", "未知错误")
                logger.error(f"❌ 四海平台Token获取失败: {error_msg}")
                return ""
                
        except Exception as e:
            logger.error(f"❌ 四海平台Token获取异常: {e}")
            return ""
