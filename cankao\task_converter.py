#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务数据转换器
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional

from models import UnifiedTask, TargetUser, TargetContent, Reward, TaskMetadata

logger = logging.getLogger(__name__)


class TaskConverter:
    """任务数据转换器"""
    
    @staticmethod
    def convert_to_unified_format(platform: str, raw_data: Dict[str, Any]) -> Optional[UnifiedTask]:
        """
        将平台原始数据转换为统一格式
        
        Args:
            platform: 平台名称 (panda, clover, sihai)
            raw_data: 平台原始数据
            
        Returns:
            统一格式的任务对象，转换失败返回None
        """
        try:
            if platform == "panda":
                return TaskConverter._convert_panda_data(raw_data)
            elif platform == "clover":
                return TaskConverter._convert_clover_data(raw_data)
            elif platform == "sihai":
                return TaskConverter._convert_sihai_data(raw_data)
            else:
                logger.error(f"❌ 不支持的平台: {platform}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 转换{platform}平台数据失败: {e}")
            return None
    
    @staticmethod
    def _convert_panda_data(raw_data: Dict[str, Any]) -> Optional[UnifiedTask]:
        """转换熊猫平台数据"""
        try:
            # 检查响应格式
            if not raw_data.get("success") or raw_data.get("code") != 0:
                logger.warning(f"⚠️ 熊猫平台无任务或响应异常: {raw_data}")
                return None
            
            data = raw_data.get("data", {})
            if not data:
                logger.warning("⚠️ 熊猫平台数据为空")
                return None
            
            params = data.get("params", {})
            
            # 构建统一任务对象
            target_user = TargetUser(
                uid=params.get("uid", ""),
                sec_uid=params.get("sec_uid", ""),
                nickname=None  # 熊猫平台不提供昵称
            )
            
            target_content = TargetContent(
                video_id=params.get("video_id", ""),
                share_url=params.get("share_url", ""),
                content_type="video"
            )
            
            reward = Reward(
                price=float(params.get("price", 0)) / 100  # 熊猫平台价格单位是分
            )
            
            metadata = TaskMetadata(
                original_platform_data=raw_data,
                created_at=datetime.now(),
                status="pending"
            )
            
            unified_task = UnifiedTask(
                task_id=data.get("studiotask_id", ""),
                platform="panda",
                task_type=TaskConverter._normalize_task_type(data.get("type", "dz")),
                target_user=target_user,
                target_content=target_content,
                reward=reward,
                metadata=metadata
            )
            
            logger.info(f"✅ 熊猫平台数据转换成功: {unified_task.task_id}")
            return unified_task
            
        except Exception as e:
            logger.error(f"❌ 熊猫平台数据转换失败: {e}")
            return None
    
    @staticmethod
    def _convert_clover_data(raw_data: Dict[str, Any]) -> Optional[UnifiedTask]:
        """转换三叶草平台数据"""
        try:
            # 检查响应格式
            if raw_data.get("code") != 0:
                logger.warning(f"⚠️ 三叶草平台无任务或响应异常: {raw_data}")
                return None
            
            data = raw_data.get("data", {})
            if not data:
                logger.warning("⚠️ 三叶草平台数据为空")
                return None
            
            # 构建统一任务对象
            target_user = TargetUser(
                uid=data.get("uid", ""),
                sec_uid=data.get("sec_uid", ""),
                nickname=None  # 三叶草平台不提供昵称
            )
            
            target_content = TargetContent(
                video_id=data.get("video_id", ""),
                share_url=data.get("share_url", ""),
                content_type="video"
            )
            
            reward = Reward(
                price=1.0  # 三叶草平台默认价格，原始数据中没有价格信息
            )
            
            metadata = TaskMetadata(
                original_platform_data=raw_data,
                created_at=datetime.now(),
                status="pending"
            )
            
            unified_task = UnifiedTask(
                task_id=data.get("task_id", ""),
                platform="clover",
                task_type=TaskConverter._normalize_task_type(data.get("task_type", "sp")),
                target_user=target_user,
                target_content=target_content,
                reward=reward,
                metadata=metadata
            )
            
            logger.info(f"✅ 三叶草平台数据转换成功: {unified_task.task_id}")
            return unified_task
            
        except Exception as e:
            logger.error(f"❌ 三叶草平台数据转换失败: {e}")
            return None
    
    @staticmethod
    def _convert_sihai_data(raw_data: Dict[str, Any]) -> Optional[UnifiedTask]:
        """转换四海平台数据"""
        try:
            # 检查响应格式
            if raw_data.get("code") != 1:  # 四海平台成功码是1
                logger.warning(f"⚠️ 四海平台无任务或响应异常: {raw_data}")
                return None
            
            data = raw_data.get("data", {})
            if not data:
                logger.warning("⚠️ 四海平台数据为空")
                return None
            
            # 构建统一任务对象
            target_user = TargetUser(
                uid=data.get("uid", ""),
                sec_uid=data.get("secUid", ""),
                nickname=data.get("nickname", "")
            )
            
            # 四海平台可能有多个URL字段，优先使用shortUrl
            share_url = data.get("shortUrl", "") or data.get("douyinUrl", "")
            
            target_content = TargetContent(
                video_id=data.get("video_id", "").split("?")[0] if data.get("video_id") else "",  # 去除URL参数
                share_url=share_url,
                content_type="video" if data.get("videoType") == "2" else "note"
            )
            
            reward = Reward(
                price=round(float(data.get("orderReceivePrice", 0)) * 100, 2)  # 四海平台价格需要乘以100，保留2位小数
            )
            
            metadata = TaskMetadata(
                original_platform_data=raw_data,
                created_at=datetime.now(),
                status="pending"
            )
            
            unified_task = UnifiedTask(
                task_id=data.get("taskLogId", ""),
                platform="sihai",
                task_type="like",  # 四海平台的platformTypeName是"点赞"
                target_user=target_user,
                target_content=target_content,
                reward=reward,
                metadata=metadata
            )
            
            logger.info(f"✅ 四海平台数据转换成功: {unified_task.task_id}")
            return unified_task
            
        except Exception as e:
            logger.error(f"❌ 四海平台数据转换失败: {e}")
            return None
    
    @staticmethod
    def _normalize_task_type(task_type: str) -> str:
        """标准化任务类型"""
        type_mapping = {
            "dz": "like",      # 点赞
            "sp": "like",      # 三叶草的点赞类型
            "gz": "follow",    # 关注
            "pl": "comment",   # 评论
            "点赞": "like",
            "关注": "follow",
            "评论": "comment"
        }
        return type_mapping.get(task_type, task_type)
    
    @staticmethod
    def convert_for_submission(unified_task: UnifiedTask) -> Dict[str, Any]:
        """
        将统一格式任务转换为提交时需要的格式
        
        Args:
            unified_task: 统一格式任务
            
        Returns:
            提交用的数据格式
        """
        return {
            "platform": "dy",  # 抖音平台标识
            "type": TaskConverter._denormalize_task_type(unified_task.task_type),
            "uid": unified_task.target_user.uid,
            "sec_uid": unified_task.target_user.sec_uid,
            "video_id": unified_task.target_content.video_id,
            "share_url": unified_task.target_content.share_url
        }
    
    @staticmethod
    def _denormalize_task_type(task_type: str) -> str:
        """反标准化任务类型"""
        type_mapping = {
            "like": "dz",
            "follow": "gz", 
            "comment": "pl"
        }
        return type_mapping.get(task_type, task_type)
