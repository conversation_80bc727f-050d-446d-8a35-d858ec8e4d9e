#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本
"""

import requests
import json
import time

# 测试数据
TOKENS = {
    "panda": "AKIDa70b4e094e386f6ad010f5ca15e97fd0",
    "clover": "AKID99fd081a6d4cb407309dd1aa788f3040", 
    "sihai": "13ea0139-5be3-4ff8-b140-06a8015a0014"
}

UID = "2159916747338847"
SEC_UID = "MS4wLjABAAAAkgfQBpKUbpa64LtiHKDirZJywNh9Lu1zXHZUOyEXN8lhtDO0eeg0v40_ktynxZ73"

def test_panda_api():
    """测试熊猫平台API"""
    print("=" * 50)
    print("测试熊猫平台API")
    print("=" * 50)
    
    url = "http://**************:8020/studio/api/task/get"
    params = {
        "key": TOKENS["panda"],
        "platform": "dy",
        "type": "dz",
        "uid": UID,
        "sec_uid": SEC_UID
    }
    
    headers = {
        'Host': '**************:8020',
        'User-Agent': 'Go-http-client/1.1',
        'Accept-Encoding': 'gzip'
    }
    
    try:
        print(f"请求URL: {url}")
        print(f"请求参数: {params}")
        print(f"请求头: {headers}")
        print()
        
        response = requests.get(url, params=params, headers=headers, timeout=10, verify=False)
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print()
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("响应JSON:")
                print(json.dumps(result, ensure_ascii=False, indent=2))
                return result
            except json.JSONDecodeError:
                print("响应内容（非JSON）:")
                print(response.text)
        else:
            print("响应内容:")
            print(response.text)
            
    except Exception as e:
        print(f"请求异常: {e}")
    
    return None

def test_clover_api():
    """测试三叶草平台API"""
    print("=" * 50)
    print("测试三叶草平台API")
    print("=" * 50)
    
    url = "http://www.sanyecao.co:98/pull"
    params = {
        "key": TOKENS["clover"],
        "uid": UID,
        "sec_uid": SEC_UID,
        "type": "dz"
    }
    
    headers = {
        'Host': 'www.sanyecao.co:98',
        'User-Agent': 'Go-http-client/1.1',
        'Accept-Encoding': 'gzip'
    }
    
    try:
        print(f"请求URL: {url}")
        print(f"请求参数: {params}")
        print(f"请求头: {headers}")
        print()
        
        response = requests.get(url, params=params, headers=headers, timeout=10)
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print()
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("响应JSON:")
                print(json.dumps(result, ensure_ascii=False, indent=2))
                return result
            except json.JSONDecodeError:
                print("响应内容（非JSON）:")
                print(response.text)
        else:
            print("响应内容:")
            print(response.text)
            
    except Exception as e:
        print(f"请求异常: {e}")
    
    return None

def test_sihai_api():
    """测试四海平台API"""
    print("=" * 50)
    print("测试四海平台API")
    print("=" * 50)
    
    url = "http://meetspace.top:2095/order/selectOneTask"
    data = {
        "platform": "2",
        "platformType": "11",
        "uid": UID,
        "uidType": "2"
    }
    
    headers = {
        "Host": "meetspace.top:2095",
        "User-Agent": "Go-http-client/1.1",
        "Accept-Encoding": "gzip",
        "Token": TOKENS["sihai"],
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    try:
        print(f"请求URL: {url}")
        print(f"请求数据: {data}")
        print(f"请求头: {headers}")
        print()
        
        response = requests.post(url, data=data, headers=headers, timeout=10)
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print()
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("响应JSON:")
                print(json.dumps(result, ensure_ascii=False, indent=2))
                return result
            except json.JSONDecodeError:
                print("响应内容（非JSON）:")
                print(response.text)
        else:
            print("响应内容:")
            print(response.text)
            
    except Exception as e:
        print(f"请求异常: {e}")
    
    return None

def main():
    """主测试函数"""
    print("🚀 开始测试3个平台的API")
    print(f"使用UID: {UID}")
    print(f"使用sec_uid: {SEC_UID}")
    print()
    
    # 测试熊猫平台
    panda_result = test_panda_api()
    print()
    time.sleep(2)
    
    # 测试三叶草平台
    clover_result = test_clover_api()
    print()
    time.sleep(2)
    
    # 测试四海平台
    sihai_result = test_sihai_api()
    print()
    
    # 总结
    print("=" * 50)
    print("测试总结")
    print("=" * 50)
    
    results = {
        "熊猫平台": panda_result is not None,
        "三叶草平台": clover_result is not None,
        "四海平台": sihai_result is not None
    }
    
    for platform, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{platform}: {status}")
    
    print()
    print("如果有API失败，请检查:")
    print("1. Token/Key是否正确")
    print("2. 网络连接是否正常")
    print("3. API端点是否可访问")
    print("4. 参数格式是否正确")

if __name__ == "__main__":
    main()
